'use client'

import { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { motion } from 'framer-motion'
import { Search, X } from 'lucide-react'

interface BlogSearchProps {
  locale: string
  initialSearch?: string
}

export default function BlogSearch({ locale, initialSearch = '' }: BlogSearchProps) {
  const [searchQuery, setSearchQuery] = useState(initialSearch)
  const [isExpanded, setIsExpanded] = useState(false)
  const router = useRouter()
  const searchParams = useSearchParams()
  
  useEffect(() => {
    setSearchQuery(initialSearch)
  }, [initialSearch])
  
  const handleSearch = (query: string) => {
    const params = new URLSearchParams(searchParams.toString())
    
    if (query.trim()) {
      params.set('search', query.trim())
    } else {
      params.delete('search')
    }
    
    params.delete('page') // 重置页码
    
    const queryString = params.toString()
    router.push(`/${locale}/blog${queryString ? `?${queryString}` : ''}`)
  }
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    handleSearch(searchQuery)
  }
  
  const handleClear = () => {
    setSearchQuery('')
    handleSearch('')
    setIsExpanded(false)
  }
  
  return (
    <div className="relative">
      <form onSubmit={handleSubmit} className="relative">
        <motion.div
          animate={{ width: isExpanded ? 320 : 280 }}
          transition={{ duration: 0.3, ease: 'easeInOut' }}
          className="relative"
        >
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onFocus={() => setIsExpanded(true)}
            onBlur={() => !searchQuery && setIsExpanded(false)}
            placeholder="Search articles..."
            className="w-full pl-12 pr-10 py-3 text-sm bg-white dark:bg-dark-800 border border-mystical-200 dark:border-dark-600 rounded-full text-mystical-800 dark:text-mystical-200 placeholder-mystical-400 dark:placeholder-mystical-500 focus:outline-none focus:ring-2 focus:ring-mystical-500 focus:border-transparent transition-all duration-200"
          />
          
          {/* 搜索图标 */}
          <div className="absolute left-4 top-1/2 transform -translate-y-1/2">
            <Search className="w-4 h-4 text-mystical-400 dark:text-mystical-500" />
          </div>
          
          {/* 清除按钮 */}
          {searchQuery && (
            <motion.button
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              type="button"
              onClick={handleClear}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 p-1 text-mystical-400 hover:text-mystical-600 dark:text-mystical-500 dark:hover:text-mystical-300 transition-colors"
            >
              <X className="w-4 h-4" />
            </motion.button>
          )}
        </motion.div>
      </form>
      
      {/* 搜索建议（可选功能） */}
      {isExpanded && searchQuery.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          className="absolute top-full left-0 right-0 mt-2 bg-white dark:bg-dark-800 border border-mystical-200 dark:border-dark-600 rounded-xl shadow-xl z-50 overflow-hidden"
        >
          <div className="p-4">
            <div className="text-xs text-mystical-500 dark:text-mystical-400 mb-2">
              Press Enter to search for "{searchQuery}"
            </div>
            
            {/* 热门搜索建议 */}
            <div className="space-y-1">
              <div className="text-xs font-medium text-mystical-600 dark:text-mystical-300 mb-2">
                Popular searches:
              </div>
              {[
                'tarot card meanings',
                'zodiac compatibility',
                'numerology calculator',
                'crystal healing',
                'palm reading guide',
                'dream interpretation'
              ].filter(suggestion => 
                suggestion.toLowerCase().includes(searchQuery.toLowerCase())
              ).slice(0, 3).map((suggestion) => (
                <button
                  key={suggestion}
                  onClick={() => {
                    setSearchQuery(suggestion)
                    handleSearch(suggestion)
                    setIsExpanded(false)
                  }}
                  className="block w-full text-left px-3 py-2 text-sm text-mystical-700 dark:text-mystical-300 hover:bg-mystical-50 dark:hover:bg-dark-700 rounded-lg transition-colors"
                >
                  <Search className="w-3 h-3 inline mr-2 text-mystical-400" />
                  {suggestion}
                </button>
              ))}
            </div>
          </div>
        </motion.div>
      )}
    </div>
  )
}
