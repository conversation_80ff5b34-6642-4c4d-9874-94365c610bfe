'use client'

import Link from 'next/link'
import Image from 'next/image'
import { motion } from 'framer-motion'
import { Clock, Eye, Star, TrendingUp } from 'lucide-react'
import type { BlogPost } from '@prisma/client'
import { formatPublishDate, formatReadingTime, getCategoryInfo } from '@/lib/blog-utils'

interface FeaturedPostsProps {
  posts: BlogPost[]
  locale: string
}

export default function FeaturedPosts({ posts, locale }: FeaturedPostsProps) {
  if (!posts || posts.length === 0) {
    return null
  }
  
  const [mainPost, ...otherPosts] = posts
  
  return (
    <section className="max-w-7xl mx-auto px-4 py-16">
      {/* 标题 */}
      <div className="text-center mb-12">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="inline-flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-mystical-500 to-gold-500 text-white rounded-full mb-4"
        >
          <TrendingUp className="w-4 h-4" />
          <span className="text-sm font-medium">Featured Articles</span>
        </motion.div>
        
        <motion.h2
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="text-3xl md:text-4xl font-bold font-serif text-mystical-900 dark:text-white mb-4"
        >
          Most Popular This Week
        </motion.h2>
        
        <motion.p
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="text-mystical-600 dark:text-mystical-300 max-w-2xl mx-auto"
        >
          Discover the most-read articles that are captivating our community
        </motion.p>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* 主要特色文章 */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="lg:col-span-2"
        >
          <FeaturedPostCard
            post={mainPost}
            locale={locale}
            size="large"
            showBadge={true}
          />
        </motion.div>
        
        {/* 次要特色文章 */}
        <div className="space-y-6">
          {otherPosts.map((post, index) => (
            <motion.div
              key={post.id}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.4 + index * 0.1 }}
            >
              <FeaturedPostCard
                post={post}
                locale={locale}
                size="small"
                rank={index + 2}
              />
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}

interface FeaturedPostCardProps {
  post: BlogPost
  locale: string
  size: 'large' | 'small'
  showBadge?: boolean
  rank?: number
}

function FeaturedPostCard({ post, locale, size, showBadge, rank }: FeaturedPostCardProps) {
  const categoryInfo = getCategoryInfo(post.category)
  
  return (
    <Link href={`/${locale}/blog/${post.slug}`}>
      <div className="group relative bg-white dark:bg-dark-800 rounded-2xl overflow-hidden shadow-lg border border-mystical-200 dark:border-dark-600 hover:shadow-xl transition-all duration-300 hover:transform hover:-translate-y-1">
        {/* 特色标签 */}
        {showBadge && (
          <div className="absolute top-4 left-4 z-10">
            <div className="flex items-center space-x-1 px-3 py-1 bg-gradient-to-r from-mystical-500 to-gold-500 text-white rounded-full text-xs font-medium">
              <Star className="w-3 h-3" />
              <span>Featured</span>
            </div>
          </div>
        )}
        
        {/* 排名标签 */}
        {rank && (
          <div className="absolute top-4 left-4 z-10">
            <div className="w-8 h-8 bg-mystical-500 text-white rounded-full flex items-center justify-center text-sm font-bold">
              {rank}
            </div>
          </div>
        )}
        
        {/* 封面图片 */}
        {post.coverImage && (
          <div className={`relative overflow-hidden ${size === 'large' ? 'aspect-video' : 'aspect-[4/3]'}`}>
            <Image
              src={post.coverImage}
              alt={post.title}
              fill
              className="object-cover group-hover:scale-105 transition-transform duration-300"
              sizes={size === 'large' ? '(max-width: 1024px) 100vw, 66vw' : '(max-width: 1024px) 100vw, 33vw'}
            />
            
            {/* 渐变遮罩 */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />
            
            {/* 分类标签 */}
            <div className="absolute bottom-4 left-4">
              <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-${categoryInfo.color}/90 text-white backdrop-blur-sm`}>
                {categoryInfo.name}
              </span>
            </div>
          </div>
        )}
        
        {/* 内容区域 */}
        <div className={`p-6 ${size === 'large' ? 'lg:p-8' : ''}`}>
          {/* 如果没有封面图片，显示分类标签 */}
          {!post.coverImage && (
            <div className="mb-3">
              <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-${categoryInfo.color}/10 text-${categoryInfo.color}`}>
                {categoryInfo.name}
              </span>
            </div>
          )}
          
          {/* 标题 */}
          <h3 className={`font-bold font-serif text-mystical-900 dark:text-white mb-3 line-clamp-2 group-hover:text-mystical-700 dark:group-hover:text-mystical-200 transition-colors ${
            size === 'large' ? 'text-2xl lg:text-3xl' : 'text-lg'
          }`}>
            {post.title}
          </h3>
          
          {/* 摘要 */}
          {post.excerpt && (
            <p className={`text-mystical-600 dark:text-mystical-300 mb-4 leading-relaxed ${
              size === 'large' ? 'line-clamp-3 text-base' : 'line-clamp-2 text-sm'
            }`}>
              {post.excerpt}
            </p>
          )}
          
          {/* 元数据 */}
          <div className="flex items-center justify-between text-xs text-mystical-500 dark:text-mystical-400">
            <div className="flex items-center space-x-4">
              <span className="flex items-center">
                <Clock className="w-3 h-3 mr-1" />
                {formatReadingTime(post.readingTime, locale)}
              </span>
              <span className="flex items-center">
                <Eye className="w-3 h-3 mr-1" />
                {post.viewCount.toLocaleString()}
              </span>
            </div>
            
            <time dateTime={post.publishedAt?.toISOString()}>
              {formatPublishDate(post.publishedAt || post.createdAt, locale)}
            </time>
          </div>
          
          {/* 标签（仅大尺寸显示） */}
          {size === 'large' && post.tags && post.tags.length > 0 && (
            <div className="flex flex-wrap gap-2 mt-4">
              {post.tags.slice(0, 4).map((tag) => (
                <span
                  key={tag}
                  className="px-2 py-1 bg-mystical-100 dark:bg-dark-700 text-mystical-600 dark:text-mystical-300 rounded text-xs"
                >
                  #{tag}
                </span>
              ))}
              {post.tags.length > 4 && (
                <span className="px-2 py-1 bg-mystical-100 dark:bg-dark-700 text-mystical-600 dark:text-mystical-300 rounded text-xs">
                  +{post.tags.length - 4}
                </span>
              )}
            </div>
          )}
        </div>
      </div>
    </Link>
  )
}
