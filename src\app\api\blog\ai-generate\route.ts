import { NextRequest, NextResponse } from 'next/server'
import { BlogService } from '@/lib/blog-service'
import { AIContentGenerator } from '@/lib/ai-content-generator'
import type { AIGeneratedPost } from '@/lib/blog-service'

/**
 * AI生成博客内容并直接入库
 * POST /api/blog/ai-generate
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // 验证必需字段
    const requiredFields = ['topic', 'locale', 'category']
    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json(
          {
            success: false,
            error: `Missing required field: ${field}`
          },
          { status: 400 }
        )
      }
    }
    
    // 使用AI内容生成器
    const generator = new AIContentGenerator()
    const aiContent = await generator.generateArticle({
      topic: body.topic,
      category: body.category,
      locale: body.locale,
      length: body.length || 'medium',
      includeTestRecommendation: body.includeTestRecommendation !== false,
      customPrompt: body.customPrompt
    })

    // 直接入库
    const post = await BlogService.createPostFromAI(aiContent)
    
    return NextResponse.json({
      success: true,
      data: {
        post,
        aiMetadata: aiContent.aiMetadata
      }
    }, { status: 201 })
    
  } catch (error) {
    console.error('AI生成内容失败:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to generate AI content',
        message: error.message
      },
      { status: 500 }
    )
  }
}

/**
 * 批量AI生成内容
 * POST /api/blog/ai-batch-generate
 */
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    
    if (!body.topics || !Array.isArray(body.topics)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Topics array is required'
        },
        { status: 400 }
      )
    }
    
    // 使用AI内容生成器批量生成
    const generator = new AIContentGenerator()
    const batchResult = await generator.batchGenerateArticles(body.topics)
    
    // 批量入库
    const result = await BlogService.batchCreateFromAI(batchResult.successful)
    
    return NextResponse.json({
      success: true,
      data: {
        ...result,
        aiGenerationFailed: batchResult.failed
      }
    }, { status: 201 })
    
  } catch (error) {
    console.error('批量AI生成内容失败:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to batch generate AI content',
        message: error.message
      },
      { status: 500 }
    )
  }
}

/**
 * 获取推荐主题
 * GET /api/blog/ai-generate/topics
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const category = searchParams.get('category')

    if (!category) {
      return NextResponse.json(
        {
          success: false,
          error: 'Category parameter is required'
        },
        { status: 400 }
      )
    }

    const topics = AIContentGenerator.getRecommendedTopics(category as any)

    return NextResponse.json({
      success: true,
      data: {
        category,
        topics
      }
    })

  } catch (error) {
    console.error('获取推荐主题失败:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to get recommended topics',
        message: error.message
      },
      { status: 500 }
    )
  }
}


