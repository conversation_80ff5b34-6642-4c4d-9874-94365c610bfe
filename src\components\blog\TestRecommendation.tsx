'use client'

import Link from 'next/link'
import { motion } from 'framer-motion'
import { 
  <PERSON><PERSON><PERSON>, 
  ArrowRight, 
  Star, 
  <PERSON>ap,
  Heart,
  <PERSON>,
  Brain,
  Gem
} from 'lucide-react'
import type { TestType } from '@prisma/client'

interface TestRecommendationProps {
  testType: TestType
  category: string
  locale: string
  context: 'article' | 'sidebar' | 'inline'
}

const testConfig = {
  TAROT: {
    title: 'Discover Your Tarot Insights',
    description: 'Unlock the mysteries of your future with our AI-powered tarot reading. Get personalized insights based on ancient wisdom.',
    icon: Sparkles,
    color: 'mystical',
    gradient: 'from-mystical-500 to-purple-600',
    benefits: [
      'Personalized card interpretations',
      'AI-powered analysis',
      'Instant results',
      'Free to use'
    ]
  },
  ASTROLOGY: {
    title: 'Explore Your Astrological Profile',
    description: 'Discover how the stars influence your personality and life path with our comprehensive astrology analysis.',
    icon: Star,
    color: 'gold',
    gradient: 'from-gold-500 to-orange-500',
    benefits: [
      'Complete birth chart analysis',
      'Personality insights',
      'Compatibility readings',
      'Daily horoscope'
    ]
  },
  NUMEROLOGY: {
    title: 'Calculate Your Life Numbers',
    description: 'Uncover the hidden meanings in your numbers and discover your life path through numerology.',
    icon: Zap,
    color: 'emerald',
    gradient: 'from-emerald-500 to-teal-500',
    benefits: [
      'Life path number calculation',
      'Personality number insights',
      'Destiny number analysis',
      'Lucky numbers'
    ]
  },
  CRYSTAL: {
    title: 'Find Your Perfect Crystal',
    description: 'Discover which crystals resonate with your energy and can support your spiritual journey.',
    icon: Gem,
    color: 'purple',
    gradient: 'from-purple-500 to-indigo-500',
    benefits: [
      'Personalized crystal recommendations',
      'Energy compatibility analysis',
      'Healing properties guide',
      'Meditation support'
    ]
  },
  PALMISTRY: {
    title: 'Read Your Palm Lines',
    description: 'Discover what your hands reveal about your personality, relationships, and future.',
    icon: Eye,
    color: 'rose',
    gradient: 'from-rose-500 to-pink-500',
    benefits: [
      'Complete palm analysis',
      'Life line interpretation',
      'Love line insights',
      'Career guidance'
    ]
  },
  DREAMS: {
    title: 'Decode Your Dreams',
    description: 'Understand the hidden messages in your dreams with our AI-powered dream interpretation.',
    icon: Brain,
    color: 'indigo',
    gradient: 'from-indigo-500 to-blue-500',
    benefits: [
      'Symbol interpretation',
      'Emotional analysis',
      'Recurring dream patterns',
      'Spiritual messages'
    ]
  }
}

export default function TestRecommendation({ 
  testType, 
  category, 
  locale, 
  context 
}: TestRecommendationProps) {
  const config = testConfig[testType]
  const Icon = config.icon
  
  const containerClasses = {
    article: 'max-w-4xl mx-auto px-4 py-16',
    sidebar: 'p-6',
    inline: 'my-8 p-6'
  }
  
  const cardClasses = {
    article: 'bg-gradient-to-br from-mystical-50 to-gold-50 dark:from-dark-800 dark:to-dark-700 rounded-3xl p-8 border border-mystical-200 dark:border-dark-600',
    sidebar: 'bg-white dark:bg-dark-800 rounded-2xl p-6 border border-mystical-200 dark:border-dark-600',
    inline: 'bg-gradient-to-r ' + config.gradient + ' rounded-2xl p-6 text-white'
  }
  
  return (
    <section className={containerClasses[context]}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className={cardClasses[context]}
      >
        <div className="text-center mb-8">
          {/* 图标 */}
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className={`inline-flex items-center justify-center w-16 h-16 rounded-full mb-4 ${
              context === 'inline' 
                ? 'bg-white/20 backdrop-blur-sm' 
                : `bg-${config.color}-100 dark:bg-${config.color}-900/20`
            }`}
          >
            <Icon className={`w-8 h-8 ${
              context === 'inline' 
                ? 'text-white' 
                : `text-${config.color}-600 dark:text-${config.color}-400`
            }`} />
          </motion.div>
          
          {/* 标题 */}
          <h3 className={`text-2xl font-bold font-serif mb-3 ${
            context === 'inline' 
              ? 'text-white' 
              : 'text-mystical-900 dark:text-white'
          }`}>
            {config.title}
          </h3>
          
          {/* 描述 */}
          <p className={`text-lg leading-relaxed mb-6 ${
            context === 'inline' 
              ? 'text-white/90' 
              : 'text-mystical-600 dark:text-mystical-300'
          }`}>
            {config.description}
          </p>
        </div>
        
        {/* 特性列表 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
          {config.benefits.map((benefit, index) => (
            <motion.div
              key={benefit}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.3 + index * 0.1 }}
              className="flex items-center space-x-3"
            >
              <div className={`w-2 h-2 rounded-full ${
                context === 'inline' 
                  ? 'bg-white/60' 
                  : `bg-${config.color}-500`
              }`} />
              <span className={`text-sm ${
                context === 'inline' 
                  ? 'text-white/90' 
                  : 'text-mystical-700 dark:text-mystical-300'
              }`}>
                {benefit}
              </span>
            </motion.div>
          ))}
        </div>
        
        {/* CTA按钮 */}
        <div className="text-center">
          <Link
            href={`/${locale}/${category.toLowerCase()}/test`}
            className={`inline-flex items-center space-x-2 px-8 py-4 rounded-full font-semibold transition-all duration-300 transform hover:scale-105 ${
              context === 'inline'
                ? 'bg-white text-mystical-700 hover:bg-mystical-50'
                : `bg-gradient-to-r ${config.gradient} text-white hover:shadow-lg`
            }`}
          >
            <span>Start Your {testType.toLowerCase()} Reading</span>
            <ArrowRight className="w-5 h-5" />
          </Link>
          
          {/* 免费标签 */}
          <div className="mt-4">
            <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${
              context === 'inline'
                ? 'bg-white/20 text-white backdrop-blur-sm'
                : 'bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400'
            }`}>
              <Heart className="w-3 h-3 mr-1" />
              100% Free • No Sign-up Required
            </span>
          </div>
        </div>
        
        {/* 统计信息 */}
        <div className="flex items-center justify-center space-x-8 mt-8 pt-6 border-t border-mystical-200 dark:border-dark-600">
          <div className="text-center">
            <div className={`text-2xl font-bold ${
              context === 'inline' 
                ? 'text-white' 
                : 'text-mystical-900 dark:text-white'
            }`}>
              50K+
            </div>
            <div className={`text-sm ${
              context === 'inline' 
                ? 'text-white/70' 
                : 'text-mystical-500 dark:text-mystical-400'
            }`}>
              Readings Done
            </div>
          </div>
          
          <div className="text-center">
            <div className={`text-2xl font-bold ${
              context === 'inline' 
                ? 'text-white' 
                : 'text-mystical-900 dark:text-white'
            }`}>
              4.9★
            </div>
            <div className={`text-sm ${
              context === 'inline' 
                ? 'text-white/70' 
                : 'text-mystical-500 dark:text-mystical-400'
            }`}>
              User Rating
            </div>
          </div>
          
          <div className="text-center">
            <div className={`text-2xl font-bold ${
              context === 'inline' 
                ? 'text-white' 
                : 'text-mystical-900 dark:text-white'
            }`}>
              2min
            </div>
            <div className={`text-sm ${
              context === 'inline' 
                ? 'text-white/70' 
                : 'text-mystical-500 dark:text-mystical-400'
            }`}>
              Average Time
            </div>
          </div>
        </div>
      </motion.div>
    </section>
  )
}
