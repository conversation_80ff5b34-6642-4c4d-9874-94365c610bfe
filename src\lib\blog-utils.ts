import type { 
  BlogPost, 
  TableOfContentsItem, 
  ReadingProgress, 
  BlogSEOConfig,
  BlogCategory,
  BLOG_CATEGORIES
} from '@/types/blog'

/**
 * 博客工具函数集合
 * 提供博客相关的通用功能
 */

/**
 * 从HTML内容生成目录
 */
export function generateTableOfContents(content: string): TableOfContentsItem[] {
  const headingRegex = /<h([1-6])[^>]*id="([^"]*)"[^>]*>(.*?)<\/h[1-6]>/gi
  const headings: TableOfContentsItem[] = []
  let match

  while ((match = headingRegex.exec(content)) !== null) {
    const level = parseInt(match[1])
    const id = match[2]
    const title = match[3].replace(/<[^>]*>/g, '') // 移除HTML标签

    headings.push({
      id,
      title,
      level,
      children: []
    })
  }

  // 构建层级结构
  return buildHierarchy(headings)
}

/**
 * 构建目录层级结构
 */
function buildHierarchy(headings: TableOfContentsItem[]): TableOfContentsItem[] {
  const result: TableOfContentsItem[] = []
  const stack: TableOfContentsItem[] = []

  for (const heading of headings) {
    // 清理栈，移除级别大于等于当前标题的项
    while (stack.length > 0 && stack[stack.length - 1].level >= heading.level) {
      stack.pop()
    }

    if (stack.length === 0) {
      // 顶级标题
      result.push(heading)
    } else {
      // 子标题
      const parent = stack[stack.length - 1]
      if (!parent.children) parent.children = []
      parent.children.push(heading)
    }

    stack.push(heading)
  }

  return result
}

/**
 * 计算阅读进度
 */
export function calculateReadingProgress(
  scrollTop: number,
  scrollHeight: number,
  clientHeight: number,
  content: string
): ReadingProgress {
  const percentage = Math.min(100, Math.max(0, (scrollTop / (scrollHeight - clientHeight)) * 100))
  
  // 计算总字数
  const textContent = content.replace(/<[^>]*>/g, '')
  const totalWords = textContent.split(/\s+/).length
  const wordsRead = Math.floor((totalWords * percentage) / 100)
  
  // 估算剩余阅读时间（假设每分钟200词）
  const remainingWords = totalWords - wordsRead
  const timeRemaining = Math.ceil(remainingWords / 200)

  return {
    percentage,
    currentSection: getCurrentSection(percentage),
    timeRemaining,
    wordsRead,
    totalWords
  }
}

/**
 * 根据进度获取当前章节
 */
function getCurrentSection(percentage: number): string {
  if (percentage < 10) return 'Introduction'
  if (percentage < 90) return 'Main Content'
  return 'Conclusion'
}

/**
 * 生成博客SEO配置
 */
export function generateBlogSEO(post: BlogPost, baseUrl: string): BlogSEOConfig {
  const canonicalUrl = `${baseUrl}/${post.locale}/blog/${post.slug}`
  
  return {
    title: post.seoTitle || post.title,
    description: post.seoDescription || post.excerpt || '',
    keywords: post.keywords || post.tags,
    ogImage: post.coverImage,
    canonicalUrl,
    structuredData: generateStructuredData(post, canonicalUrl)
  }
}

/**
 * 生成结构化数据
 */
function generateStructuredData(post: BlogPost, canonicalUrl: string) {
  return {
    '@context': 'https://schema.org',
    '@type': 'Article',
    headline: post.title,
    description: post.excerpt,
    image: post.coverImage,
    author: {
      '@type': 'Organization',
      name: 'Mystical Website'
    },
    publisher: {
      '@type': 'Organization',
      name: 'Mystical Website',
      logo: {
        '@type': 'ImageObject',
        url: `${canonicalUrl}/logo.png`
      }
    },
    datePublished: post.publishedAt,
    dateModified: post.updatedAt,
    mainEntityOfPage: {
      '@type': 'WebPage',
      '@id': canonicalUrl
    },
    articleSection: post.category,
    keywords: post.keywords?.join(', '),
    wordCount: calculateWordCount(post.content),
    timeRequired: `PT${post.readingTime}M`
  }
}

/**
 * 计算字数
 */
export function calculateWordCount(content: string): number {
  const textContent = content.replace(/<[^>]*>/g, '')
  return textContent.split(/\s+/).filter(word => word.length > 0).length
}

/**
 * 格式化发布日期
 */
export function formatPublishDate(date: Date | string, locale: string = 'en'): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  
  const options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }
  
  return dateObj.toLocaleDateString(locale, options)
}

/**
 * 格式化阅读时间
 */
export function formatReadingTime(minutes: number, locale: string = 'en'): string {
  if (locale.startsWith('zh')) {
    return `${minutes} 分钟阅读`
  }
  
  return `${minutes} min read`
}

/**
 * 获取分类信息
 */
export function getCategoryInfo(category: string) {
  return BLOG_CATEGORIES[category as BlogCategory] || {
    name: category,
    slug: category,
    description: '',
    color: 'gray-500',
    relatedTest: null
  }
}

/**
 * 生成文章摘要
 */
export function generateExcerpt(content: string, maxLength: number = 160): string {
  // 移除HTML标签
  const textContent = content.replace(/<[^>]*>/g, '')
  
  if (textContent.length <= maxLength) {
    return textContent
  }
  
  // 在句子边界截取
  const sentences = textContent.split(/[.!?]+/)
  let excerpt = ''
  
  for (const sentence of sentences) {
    if ((excerpt + sentence).length > maxLength) {
      break
    }
    excerpt += sentence + '. '
  }
  
  return excerpt.trim() || textContent.substring(0, maxLength) + '...'
}

/**
 * 提取文章中的图片
 */
export function extractImagesFromContent(content: string): Array<{
  src: string
  alt: string
  caption?: string
}> {
  const imgRegex = /<img[^>]+src="([^"]*)"[^>]*alt="([^"]*)"[^>]*>/gi
  const images: Array<{ src: string; alt: string; caption?: string }> = []
  let match

  while ((match = imgRegex.exec(content)) !== null) {
    images.push({
      src: match[1],
      alt: match[2]
    })
  }

  return images
}

/**
 * 清理HTML内容
 */
export function sanitizeContent(content: string): string {
  // 允许的HTML标签
  const allowedTags = [
    'p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
    'strong', 'em', 'u', 'strike',
    'ul', 'ol', 'li',
    'a', 'img',
    'blockquote', 'code', 'pre',
    'br', 'hr'
  ]
  
  // 这里应该使用专门的HTML清理库，如DOMPurify
  // 简化版本，实际项目中需要更严格的清理
  return content
}

/**
 * 生成相关文章推荐
 */
export function generateRelatedPostsQuery(
  currentPost: BlogPost,
  maxCount: number = 3
): {
  category: string
  tags: string[]
  excludeId: string
  locale: string
  limit: number
} {
  return {
    category: currentPost.category,
    tags: currentPost.tags,
    excludeId: currentPost.id,
    locale: currentPost.locale,
    limit: maxCount
  }
}

/**
 * 计算内容相似度
 */
export function calculateContentSimilarity(content1: string, content2: string): number {
  // 简化的相似度计算，实际项目中可以使用更复杂的算法
  const words1 = new Set(content1.toLowerCase().split(/\s+/))
  const words2 = new Set(content2.toLowerCase().split(/\s+/))
  
  const intersection = new Set([...words1].filter(x => words2.has(x)))
  const union = new Set([...words1, ...words2])
  
  return intersection.size / union.size
}

/**
 * 生成社交分享链接
 */
export function generateSocialShareLinks(post: BlogPost, baseUrl: string) {
  const url = `${baseUrl}/${post.locale}/blog/${post.slug}`
  const text = post.title
  const hashtags = post.tags.join(',')
  
  return {
    twitter: `https://twitter.com/intent/tweet?url=${encodeURIComponent(url)}&text=${encodeURIComponent(text)}&hashtags=${encodeURIComponent(hashtags)}`,
    facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`,
    linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}`,
    pinterest: `https://pinterest.com/pin/create/button/?url=${encodeURIComponent(url)}&description=${encodeURIComponent(text)}`,
    whatsapp: `https://wa.me/?text=${encodeURIComponent(text + ' ' + url)}`
  }
}

/**
 * 验证博客内容质量
 */
export function validateContentQuality(post: BlogPost): {
  score: number
  issues: string[]
  suggestions: string[]
} {
  const issues: string[] = []
  const suggestions: string[] = []
  let score = 100

  // 标题长度检查
  if (post.title.length < 10) {
    issues.push('Title too short')
    suggestions.push('Consider a more descriptive title (10+ characters)')
    score -= 10
  }
  
  if (post.title.length > 60) {
    issues.push('Title too long for SEO')
    suggestions.push('Keep title under 60 characters for better SEO')
    score -= 5
  }

  // 内容长度检查
  const wordCount = calculateWordCount(post.content)
  if (wordCount < 300) {
    issues.push('Content too short')
    suggestions.push('Add more content (aim for 300+ words)')
    score -= 20
  }

  // 摘要检查
  if (!post.excerpt || post.excerpt.length < 50) {
    issues.push('Missing or short excerpt')
    suggestions.push('Add a compelling excerpt (50+ characters)')
    score -= 10
  }

  // 标签检查
  if (!post.tags || post.tags.length === 0) {
    issues.push('No tags')
    suggestions.push('Add relevant tags for better discoverability')
    score -= 5
  }

  // 封面图片检查
  if (!post.coverImage) {
    issues.push('No cover image')
    suggestions.push('Add an attractive cover image')
    score -= 10
  }

  return {
    score: Math.max(0, score),
    issues,
    suggestions
  }
}

/**
 * 生成面包屑导航
 */
export function generateBreadcrumbs(post: BlogPost, locale: string) {
  const categoryInfo = getCategoryInfo(post.category)
  
  return [
    { name: 'Home', href: `/${locale}` },
    { name: 'Blog', href: `/${locale}/blog` },
    { name: categoryInfo.name, href: `/${locale}/blog?category=${post.category}` },
    { name: post.title, href: `/${locale}/blog/${post.slug}` }
  ]
}
