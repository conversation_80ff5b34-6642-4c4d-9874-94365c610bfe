'use client'

import { useState, useEffect, useRef } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Clock, 
  Eye, 
  Calendar, 
  Share2, 
  Heart, 
  Bookmark, 
  MessageCircle,
  ChevronUp,
  List,
  Sun,
  Moon
} from 'lucide-react'
import { useTheme } from 'next-themes'
import type { BlogPost } from '@prisma/client'
import { formatPublishDate, formatReadingTime, generateTableOfContents } from '@/lib/blog-utils'
import { AnimatedReadingProgress } from './ReadingProgress'
import { TableOfContents } from './TableOfContents'
import SocialShare from './SocialShare'
import InteractionButtons from './InteractionButtons'

interface BlogArticleProps {
  post: BlogPost
  locale: string
  categoryInfo: {
    name: string
    color: string
    relatedTest?: string
  }
}

export default function BlogArticle({ post, locale, categoryInfo }: BlogArticleProps) {
  const [isScrolled, setIsScrolled] = useState(false)
  const [showTOC, setShowTOC] = useState(false)
  const [readingProgress, setReadingProgress] = useState(0)
  const [currentSection, setCurrentSection] = useState('')
  const [showBackToTop, setShowBackToTop] = useState(false)
  const [focusMode, setFocusMode] = useState(false)
  
  const articleRef = useRef<HTMLElement>(null)
  const { theme, setTheme } = useTheme()
  
  // 生成目录
  const tableOfContents = generateTableOfContents(post.content)
  
  // 滚动监听
  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.scrollY
      const scrollHeight = document.documentElement.scrollHeight
      const clientHeight = window.innerHeight
      
      // 更新滚动状态
      setIsScrolled(scrollTop > 100)
      setShowBackToTop(scrollTop > 500)
      
      // 计算阅读进度
      const progress = Math.min(100, (scrollTop / (scrollHeight - clientHeight)) * 100)
      setReadingProgress(progress)
      
      // 更新当前章节
      if (articleRef.current) {
        const headings = articleRef.current.querySelectorAll('h1, h2, h3, h4, h5, h6')
        let current = ''
        
        headings.forEach((heading) => {
          const rect = heading.getBoundingClientRect()
          if (rect.top <= 100) {
            current = heading.id || heading.textContent || ''
          }
        })
        
        setCurrentSection(current)
      }
    }
    
    window.addEventListener('scroll', handleScroll, { passive: true })
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])
  
  // 回到顶部
  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }
  
  // 切换专注模式
  const toggleFocusMode = () => {
    setFocusMode(!focusMode)
  }
  
  return (
    <>
      {/* 阅读进度条 */}
      <AnimatedReadingProgress />
      
      {/* 主要内容容器 - Medium标准680px宽度 */}
      <div className={`relative ${focusMode ? 'focus-mode' : ''}`}>
        <div className="max-w-4xl mx-auto px-4 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-12 gap-8">
            {/* 左侧互动按钮（桌面端） */}
            <div className="hidden lg:block lg:col-span-1">
              <div className="sticky top-24">
                <InteractionButtons
                  postId={post.id}
                  initialLikes={0}
                  initialBookmarks={0}
                  isLiked={false}
                  isBookmarked={false}
                />
              </div>
            </div>
            
            {/* 主要文章内容 - 680px最佳阅读宽度 */}
            <article 
              ref={articleRef}
              className="lg:col-span-8 max-w-[680px] mx-auto"
            >
              {/* 文章头部 */}
              <header className="mb-12">
                {/* 分类标签 */}
                <div className="mb-4">
                  <Link
                    href={`/${locale}/blog?category=${post.category}`}
                    className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-${categoryInfo.color}/10 text-${categoryInfo.color} hover:bg-${categoryInfo.color}/20 transition-colors`}
                  >
                    {categoryInfo.name}
                  </Link>
                </div>
                
                {/* 文章标题 - Medium风格 */}
                <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold font-serif text-mystical-900 dark:text-white leading-tight mb-6 tracking-tight">
                  {post.title}
                </h1>
                
                {/* 文章副标题/摘要 */}
                {post.excerpt && (
                  <p className="text-xl text-mystical-600 dark:text-mystical-300 leading-relaxed mb-8 font-light italic">
                    {post.excerpt}
                  </p>
                )}
                
                {/* 作者信息和元数据 */}
                <div className="flex items-center justify-between py-6 border-t border-b border-mystical-200 dark:border-dark-700">
                  <div className="flex items-center space-x-4">
                    {/* 作者头像 */}
                    <div className="w-12 h-12 rounded-full bg-gradient-to-br from-mystical-400 to-gold-400 flex items-center justify-center">
                      <span className="text-white font-semibold text-lg">M</span>
                    </div>
                    
                    <div>
                      <p className="font-medium text-mystical-900 dark:text-white">
                        Mystical Website Team
                      </p>
                      <div className="flex items-center space-x-4 text-sm text-mystical-500 dark:text-mystical-400">
                        <span className="flex items-center">
                          <Calendar className="w-4 h-4 mr-1" />
                          {formatPublishDate(post.publishedAt || post.createdAt, locale)}
                        </span>
                        <span className="flex items-center">
                          <Clock className="w-4 h-4 mr-1" />
                          {formatReadingTime(post.readingTime, locale)}
                        </span>
                        <span className="flex items-center">
                          <Eye className="w-4 h-4 mr-1" />
                          {post.viewCount} views
                        </span>
                      </div>
                    </div>
                  </div>
                  
                  {/* 分享按钮 */}
                  <SocialShare
                    post={post}
                    locale={locale}
                    className="hidden md:block"
                  />
                </div>
              </header>
              
              {/* 封面图片 */}
              {post.coverImage && (
                <div className="mb-12">
                  <div className="relative aspect-video rounded-2xl overflow-hidden">
                    <Image
                      src={post.coverImage}
                      alt={post.title}
                      fill
                      className="object-cover"
                      priority
                      sizes="(max-width: 768px) 100vw, 680px"
                    />
                  </div>
                </div>
              )}
              
              {/* 文章内容 - Medium标准1.75倍行高 */}
              <div 
                className="prose prose-lg max-w-none
                  prose-headings:font-serif prose-headings:font-bold prose-headings:text-mystical-900 dark:prose-headings:text-white
                  prose-p:text-mystical-700 dark:prose-p:text-mystical-200 prose-p:leading-[1.75] prose-p:text-lg prose-p:mb-6
                  prose-a:text-mystical-600 prose-a:no-underline hover:prose-a:underline
                  prose-strong:text-mystical-900 dark:prose-strong:text-white
                  prose-blockquote:border-l-4 prose-blockquote:border-mystical-300 prose-blockquote:bg-mystical-50 dark:prose-blockquote:bg-dark-800 prose-blockquote:py-4 prose-blockquote:px-6 prose-blockquote:rounded-r-lg
                  prose-code:bg-mystical-100 dark:prose-code:bg-dark-700 prose-code:px-2 prose-code:py-1 prose-code:rounded prose-code:text-sm
                  prose-pre:bg-dark-800 prose-pre:text-mystical-100
                  prose-ul:space-y-2 prose-ol:space-y-2
                  prose-li:text-mystical-700 dark:prose-li:text-mystical-200
                  prose-img:rounded-xl prose-img:shadow-lg"
                dangerouslySetInnerHTML={{ __html: post.content }}
              />
              
              {/* 文章标签 */}
              {post.tags && post.tags.length > 0 && (
                <div className="mt-12 pt-8 border-t border-mystical-200 dark:border-dark-700">
                  <div className="flex flex-wrap gap-2">
                    {post.tags.map((tag) => (
                      <Link
                        key={tag}
                        href={`/${locale}/blog?tag=${tag}`}
                        className="px-3 py-1 bg-mystical-100 dark:bg-dark-700 text-mystical-600 dark:text-mystical-300 rounded-full text-sm hover:bg-mystical-200 dark:hover:bg-dark-600 transition-colors"
                      >
                        #{tag}
                      </Link>
                    ))}
                  </div>
                </div>
              )}
            </article>
            
            {/* 右侧目录（桌面端） */}
            <div className="hidden lg:block lg:col-span-3">
              <div className="sticky top-24">
                <TableOfContents
                  items={tableOfContents}
                  currentSection={currentSection}
                  className="mb-8"
                />
              </div>
            </div>
          </div>
        </div>
        
        {/* 浮动操作按钮 */}
        <div className="fixed bottom-6 right-6 flex flex-col space-y-3 z-50">
          {/* 目录按钮（移动端） */}
          <motion.button
            className="lg:hidden w-12 h-12 bg-white dark:bg-dark-800 rounded-full shadow-lg border border-mystical-200 dark:border-dark-600 flex items-center justify-center text-mystical-600 dark:text-mystical-400 hover:text-mystical-700 dark:hover:text-mystical-300 transition-colors"
            onClick={() => setShowTOC(!showTOC)}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <List className="w-5 h-5" />
          </motion.button>
          
          {/* 专注模式切换 */}
          <motion.button
            className="w-12 h-12 bg-white dark:bg-dark-800 rounded-full shadow-lg border border-mystical-200 dark:border-dark-600 flex items-center justify-center text-mystical-600 dark:text-mystical-400 hover:text-mystical-700 dark:hover:text-mystical-300 transition-colors"
            onClick={toggleFocusMode}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            {focusMode ? <Eye className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
          </motion.button>
          
          {/* 主题切换 */}
          <motion.button
            className="w-12 h-12 bg-white dark:bg-dark-800 rounded-full shadow-lg border border-mystical-200 dark:border-dark-600 flex items-center justify-center text-mystical-600 dark:text-mystical-400 hover:text-mystical-700 dark:hover:text-mystical-300 transition-colors"
            onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            {theme === 'dark' ? <Sun className="w-5 h-5" /> : <Moon className="w-5 h-5" />}
          </motion.button>
          
          {/* 回到顶部 */}
          <AnimatePresence>
            {showBackToTop && (
              <motion.button
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                className="w-12 h-12 bg-mystical-500 rounded-full shadow-lg flex items-center justify-center text-white hover:bg-mystical-600 transition-colors"
                onClick={scrollToTop}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <ChevronUp className="w-5 h-5" />
              </motion.button>
            )}
          </AnimatePresence>
        </div>
        
        {/* 移动端目录弹窗 */}
        <AnimatePresence>
          {showTOC && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="lg:hidden fixed inset-0 bg-black/50 z-50"
              onClick={() => setShowTOC(false)}
            >
              <motion.div
                initial={{ x: '100%' }}
                animate={{ x: 0 }}
                exit={{ x: '100%' }}
                className="absolute right-0 top-0 h-full w-80 bg-white dark:bg-dark-800 shadow-xl p-6 overflow-y-auto"
                onClick={(e) => e.stopPropagation()}
              >
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-semibold text-mystical-900 dark:text-white">
                    Table of Contents
                  </h3>
                  <button
                    onClick={() => setShowTOC(false)}
                    className="text-mystical-500 hover:text-mystical-700 dark:hover:text-mystical-300"
                  >
                    ×
                  </button>
                </div>
                <TableOfContents
                  items={tableOfContents}
                  currentSection={currentSection}
                  onItemClick={() => setShowTOC(false)}
                />
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
      
      {/* 专注模式样式 */}
      <style jsx>{`
        .focus-mode {
          .lg\\:col-span-1,
          .lg\\:col-span-3 {
            display: none !important;
          }
          
          .lg\\:col-span-8 {
            grid-column: span 12 / span 12 !important;
          }
          
          article {
            max-width: 800px !important;
          }
          
          .prose p {
            font-size: 1.125rem !important;
            line-height: 1.8 !important;
          }
        }
      `}</style>
    </>
  )
}
