import type { TestType } from '@prisma/client'
import { BLOG_CATEGORIES } from '@/types/blog'

// 测试导流策略配置
export interface TestIntegrationConfig {
  testType: TestType
  placement: ('intro' | 'middle' | 'conclusion')[]
  ctaText: string
  ctaStyle: 'inline' | 'card' | 'banner'
  triggerWords: string[]
  contextualRelevance: number // 0-1, 相关性评分
}

// 测试导流规则
const TEST_INTEGRATION_RULES: Record<string, TestIntegrationConfig[]> = {
  tarot: [
    {
      testType: 'TAROT',
      placement: ['intro', 'conclusion'],
      ctaText: 'Discover Your Personal Tarot Reading',
      ctaStyle: 'card',
      triggerWords: ['tarot', 'cards', 'reading', 'divination', 'guidance', 'future'],
      contextualRelevance: 1.0
    }
  ],
  astrology: [
    {
      testType: 'ASTROLOGY',
      placement: ['intro', 'conclusion'],
      ctaText: 'Get Your Complete Astrological Analysis',
      ctaStyle: 'card',
      triggerWords: ['astrology', 'zodiac', 'horoscope', 'birth chart', 'planets', 'signs'],
      contextualRelevance: 1.0
    }
  ],
  numerology: [
    {
      testType: 'NUMEROLOGY',
      placement: ['intro', 'conclusion'],
      ctaText: 'Calculate Your Life Path Numbers',
      ctaStyle: 'card',
      triggerWords: ['numerology', 'numbers', 'life path', 'destiny', 'calculation'],
      contextualRelevance: 1.0
    }
  ],
  crystal: [
    {
      testType: 'CRYSTAL',
      placement: ['intro', 'conclusion'],
      ctaText: 'Find Your Perfect Crystal Match',
      ctaStyle: 'card',
      triggerWords: ['crystal', 'healing', 'energy', 'chakra', 'meditation', 'stones'],
      contextualRelevance: 1.0
    }
  ],
  palmistry: [
    {
      testType: 'PALMISTRY',
      placement: ['intro', 'conclusion'],
      ctaText: 'Get Your Palm Reading Analysis',
      ctaStyle: 'card',
      triggerWords: ['palmistry', 'palm reading', 'hands', 'lines', 'fate line', 'life line'],
      contextualRelevance: 1.0
    }
  ],
  dreams: [
    {
      testType: 'DREAMS',
      placement: ['intro', 'conclusion'],
      ctaText: 'Decode Your Dream Meanings',
      ctaStyle: 'card',
      triggerWords: ['dreams', 'dream interpretation', 'symbols', 'subconscious', 'sleep'],
      contextualRelevance: 1.0
    }
  ]
}

// 跨类别测试推荐
const CROSS_CATEGORY_RECOMMENDATIONS: Record<string, TestIntegrationConfig[]> = {
  tarot: [
    {
      testType: 'ASTROLOGY',
      placement: ['conclusion'],
      ctaText: 'Explore Your Astrological Profile',
      ctaStyle: 'inline',
      triggerWords: ['personality', 'traits', 'future', 'guidance'],
      contextualRelevance: 0.7
    },
    {
      testType: 'NUMEROLOGY',
      placement: ['conclusion'],
      ctaText: 'Discover Your Life Path Numbers',
      ctaStyle: 'inline',
      triggerWords: ['destiny', 'path', 'numbers', 'meaning'],
      contextualRelevance: 0.6
    }
  ],
  astrology: [
    {
      testType: 'TAROT',
      placement: ['conclusion'],
      ctaText: 'Get Your Tarot Card Reading',
      ctaStyle: 'inline',
      triggerWords: ['guidance', 'insight', 'future', 'divination'],
      contextualRelevance: 0.7
    },
    {
      testType: 'NUMEROLOGY',
      placement: ['conclusion'],
      ctaText: 'Calculate Your Personal Numbers',
      ctaStyle: 'inline',
      triggerWords: ['birth date', 'numbers', 'calculation', 'personality'],
      contextualRelevance: 0.8
    }
  ],
  numerology: [
    {
      testType: 'ASTROLOGY',
      placement: ['conclusion'],
      ctaText: 'Explore Your Birth Chart',
      ctaStyle: 'inline',
      triggerWords: ['birth', 'personality', 'traits', 'cosmic'],
      contextualRelevance: 0.8
    },
    {
      testType: 'TAROT',
      placement: ['conclusion'],
      ctaText: 'Get Your Tarot Guidance',
      ctaStyle: 'inline',
      triggerWords: ['guidance', 'insight', 'spiritual', 'wisdom'],
      contextualRelevance: 0.6
    }
  ]
}

/**
 * 测试集成服务类
 */
export class TestIntegrationService {
  /**
   * 获取文章的测试推荐
   */
  static getTestRecommendations(
    category: string,
    content: string,
    title: string,
    tags: string[]
  ): TestIntegrationConfig[] {
    const recommendations: TestIntegrationConfig[] = []
    
    // 1. 获取主要类别的测试推荐
    const primaryRecommendations = TEST_INTEGRATION_RULES[category] || []
    recommendations.push(...primaryRecommendations)
    
    // 2. 获取跨类别推荐
    const crossRecommendations = CROSS_CATEGORY_RECOMMENDATIONS[category] || []
    const relevantCrossRecommendations = crossRecommendations.filter(rec => 
      this.calculateRelevance(rec, content, title, tags) > 0.5
    )
    recommendations.push(...relevantCrossRecommendations)
    
    // 3. 按相关性排序
    return recommendations.sort((a, b) => b.contextualRelevance - a.contextualRelevance)
  }
  
  /**
   * 计算测试推荐的相关性
   */
  private static calculateRelevance(
    recommendation: TestIntegrationConfig,
    content: string,
    title: string,
    tags: string[]
  ): number {
    const text = (content + ' ' + title + ' ' + tags.join(' ')).toLowerCase()
    
    let relevanceScore = recommendation.contextualRelevance
    let matchCount = 0
    
    // 检查触发词匹配
    for (const triggerWord of recommendation.triggerWords) {
      if (text.includes(triggerWord.toLowerCase())) {
        matchCount++
      }
    }
    
    // 根据匹配度调整相关性
    const matchRatio = matchCount / recommendation.triggerWords.length
    relevanceScore *= (0.5 + matchRatio * 0.5) // 基础分数50% + 匹配度50%
    
    return relevanceScore
  }
  
  /**
   * 生成测试CTA内容
   */
  static generateTestCTA(
    recommendation: TestIntegrationConfig,
    placement: 'intro' | 'middle' | 'conclusion',
    locale: string = 'en'
  ): {
    html: string
    style: string
  } {
    const testInfo = this.getTestInfo(recommendation.testType)
    const localizedContent = this.getLocalizedContent(recommendation, locale)
    
    const baseStyles = {
      inline: 'my-6 p-4 bg-gradient-to-r from-mystical-50 to-gold-50 dark:from-dark-800 dark:to-dark-700 rounded-lg border border-mystical-200 dark:border-dark-600',
      card: 'my-8 p-6 bg-gradient-to-br from-mystical-50 to-gold-50 dark:from-dark-800 dark:to-dark-700 rounded-2xl border border-mystical-200 dark:border-dark-600 shadow-lg',
      banner: 'my-8 p-8 bg-gradient-to-r from-mystical-500 to-gold-500 rounded-2xl text-white shadow-xl'
    }
    
    const ctaTemplates = {
      intro: {
        title: `Ready to explore ${testInfo.name.toLowerCase()}?`,
        description: `Before we dive deeper, discover your personal ${testInfo.name.toLowerCase()} insights with our AI-powered analysis.`,
        buttonText: localizedContent.ctaText
      },
      middle: {
        title: `Curious about your personal connection?`,
        description: `Take a moment to explore how these insights apply to your unique situation.`,
        buttonText: `Get Your ${testInfo.name} Reading`
      },
      conclusion: {
        title: `Take the next step in your journey`,
        description: `Now that you've learned about this topic, discover your personalized insights and guidance.`,
        buttonText: localizedContent.ctaText
      }
    }
    
    const template = ctaTemplates[placement]
    const testUrl = `/${locale}/${testInfo.category}/test`
    
    const html = `
      <div class="test-integration-cta ${baseStyles[recommendation.ctaStyle]}">
        <div class="flex items-start space-x-4">
          <div class="flex-shrink-0">
            <div class="w-12 h-12 bg-${testInfo.color}-500 rounded-full flex items-center justify-center">
              <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                ${testInfo.icon}
              </svg>
            </div>
          </div>
          <div class="flex-1">
            <h3 class="text-lg font-semibold text-mystical-900 dark:text-white mb-2">
              ${template.title}
            </h3>
            <p class="text-mystical-600 dark:text-mystical-300 mb-4">
              ${template.description}
            </p>
            <a 
              href="${testUrl}" 
              class="inline-flex items-center px-6 py-3 bg-${testInfo.color}-500 text-white rounded-full hover:bg-${testInfo.color}-600 transition-colors font-medium"
              onclick="trackTestCTA('${recommendation.testType}', '${placement}')"
            >
              ${template.buttonText}
              <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
              </svg>
            </a>
          </div>
        </div>
        
        <!-- 统计信息 -->
        <div class="mt-4 pt-4 border-t border-mystical-200 dark:border-dark-600">
          <div class="flex items-center justify-between text-sm text-mystical-500 dark:text-mystical-400">
            <span>✨ Free • 2 minutes • AI-powered</span>
            <span>🔥 ${this.getTestStats(recommendation.testType).completions}+ completed</span>
          </div>
        </div>
      </div>
    `
    
    return {
      html,
      style: baseStyles[recommendation.ctaStyle]
    }
  }
  
  /**
   * 获取测试信息
   */
  private static getTestInfo(testType: TestType) {
    const testInfoMap = {
      TAROT: {
        name: 'Tarot',
        category: 'tarot',
        color: 'mystical',
        icon: '<path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>'
      },
      ASTROLOGY: {
        name: 'Astrology',
        category: 'astrology',
        color: 'gold',
        icon: '<path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>'
      },
      NUMEROLOGY: {
        name: 'Numerology',
        category: 'numerology',
        color: 'emerald',
        icon: '<path d="M7 20l4-16m2 16l4-16M6 9h14M4 15h14"/>'
      },
      CRYSTAL: {
        name: 'Crystal Healing',
        category: 'crystal',
        color: 'purple',
        icon: '<path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>'
      },
      PALMISTRY: {
        name: 'Palmistry',
        category: 'palmistry',
        color: 'rose',
        icon: '<path d="M7 4V2a1 1 0 0 1 2 0v2h2V2a1 1 0 0 1 2 0v2h2V2a1 1 0 0 1 2 0v2h1a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h1z"/>'
      },
      DREAMS: {
        name: 'Dream Analysis',
        category: 'dreams',
        color: 'indigo',
        icon: '<path d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"/>'
      }
    }
    
    return testInfoMap[testType]
  }
  
  /**
   * 获取本地化内容
   */
  private static getLocalizedContent(recommendation: TestIntegrationConfig, locale: string) {
    // 简化版本，实际项目中应该有完整的国际化支持
    const localizations = {
      'zh-CN': {
        ctaText: recommendation.ctaText.replace('Get Your', '获取您的').replace('Discover Your', '发现您的').replace('Calculate Your', '计算您的').replace('Find Your', '找到您的').replace('Decode Your', '解读您的')
      },
      'zh-TW': {
        ctaText: recommendation.ctaText.replace('Get Your', '獲取您的').replace('Discover Your', '發現您的').replace('Calculate Your', '計算您的').replace('Find Your', '找到您的').replace('Decode Your', '解讀您的')
      }
    }
    
    return localizations[locale as keyof typeof localizations] || { ctaText: recommendation.ctaText }
  }
  
  /**
   * 获取测试统计信息
   */
  private static getTestStats(testType: TestType) {
    // 模拟统计数据，实际项目中应该从数据库获取
    const statsMap = {
      TAROT: { completions: 15420 },
      ASTROLOGY: { completions: 23150 },
      NUMEROLOGY: { completions: 18900 },
      CRYSTAL: { completions: 12300 },
      PALMISTRY: { completions: 9800 },
      DREAMS: { completions: 11200 }
    }
    
    return statsMap[testType]
  }
  
  /**
   * 在内容中插入测试CTA
   */
  static insertTestCTAs(
    content: string,
    recommendations: TestIntegrationConfig[],
    locale: string = 'en'
  ): string {
    let processedContent = content
    
    // 为每个推荐的位置插入CTA
    for (const recommendation of recommendations) {
      for (const placement of recommendation.placement) {
        const cta = this.generateTestCTA(recommendation, placement, locale)
        processedContent = this.insertCTAAtPosition(processedContent, cta.html, placement)
      }
    }
    
    return processedContent
  }
  
  /**
   * 在指定位置插入CTA
   */
  private static insertCTAAtPosition(content: string, ctaHtml: string, placement: 'intro' | 'middle' | 'conclusion'): string {
    switch (placement) {
      case 'intro':
        // 在第一个段落后插入
        return content.replace(/(<p[^>]*>.*?<\/p>)/, `$1\n${ctaHtml}`)
      
      case 'middle':
        // 在内容中间插入
        const paragraphs = content.split('</p>')
        const middleIndex = Math.floor(paragraphs.length / 2)
        paragraphs.splice(middleIndex, 0, ctaHtml)
        return paragraphs.join('</p>')
      
      case 'conclusion':
        // 在最后一个段落前插入
        return content.replace(/(<\/p>\s*$)/, `\n${ctaHtml}$1`)
      
      default:
        return content
    }
  }
}
