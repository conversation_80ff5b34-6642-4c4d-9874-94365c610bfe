import type { BlogPost, PostStatus, TestType } from '@prisma/client'

// 博客文章扩展类型
export interface BlogPostWithMeta extends BlogPost {
  author?: {
    name: string
    avatar?: string
  }
  relatedPosts?: BlogPost[]
  recommendedTests?: TestType[]
}

// 博客列表项类型
export interface BlogListItem {
  id: string
  title: string
  slug: string
  excerpt: string
  coverImage?: string
  category: string
  tags: string[]
  publishedAt: Date
  readingTime: number
  viewCount: number
  author?: {
    name: string
    avatar?: string
  }
}

// 博客分类配置
export const BLOG_CATEGORIES = {
  tarot: {
    name: 'Tarot',
    slug: 'tarot',
    description: 'Tarot card readings and interpretations',
    color: 'mystical-500',
    relatedTest: 'TAROT' as TestType
  },
  astrology: {
    name: 'Astrology',
    slug: 'astrology',
    description: 'Zodiac signs and astrological insights',
    color: 'gold-500',
    relatedTest: 'ASTROLOGY' as TestType
  },
  numerology: {
    name: 'Numerology',
    slug: 'numerology',
    description: 'Numbers and their mystical meanings',
    color: 'emerald-500',
    relatedTest: 'NUMEROLOGY' as TestType
  },
  crystal: {
    name: 'Crystal Healing',
    slug: 'crystal',
    description: 'Crystal energy and healing properties',
    color: 'purple-500',
    relatedTest: 'CRYSTAL' as TestType
  },
  palmistry: {
    name: 'Palmistry',
    slug: 'palmistry',
    description: 'Palm reading and hand analysis',
    color: 'rose-500',
    relatedTest: 'PALMISTRY' as TestType
  },
  dreams: {
    name: 'Dream Analysis',
    slug: 'dreams',
    description: 'Dream interpretation and meanings',
    color: 'indigo-500',
    relatedTest: 'DREAMS' as TestType
  }
} as const

export type BlogCategory = keyof typeof BLOG_CATEGORIES

// 博客SEO配置
export interface BlogSEOConfig {
  title: string
  description: string
  keywords: string[]
  ogImage?: string
  canonicalUrl?: string
  structuredData?: any
}

// 博客内容结构
export interface BlogContentStructure {
  introduction: string
  mainContent: string
  conclusion: string
  callToAction?: {
    text: string
    link: string
    testType?: TestType
  }
}

// 阅读进度类型
export interface ReadingProgress {
  percentage: number
  currentSection: string
  timeRemaining: number
  wordsRead: number
  totalWords: number
}

// 目录项类型
export interface TableOfContentsItem {
  id: string
  title: string
  level: number
  children?: TableOfContentsItem[]
}

// 博客互动数据
export interface BlogInteractionData {
  likes: number
  shares: number
  comments: number
  bookmarks: number
  isLiked: boolean
  isBookmarked: boolean
}

// 相关文章推荐配置
export interface RelatedPostsConfig {
  maxCount: number
  matchBy: ('category' | 'tags' | 'locale')[]
  excludeCurrentPost: boolean
}

// 博客搜索结果
export interface BlogSearchResult {
  posts: BlogListItem[]
  total: number
  query: string
  suggestions?: string[]
  filters: {
    categories: string[]
    tags: string[]
    dateRange?: {
      start: Date
      end: Date
    }
  }
}

// 博客统计数据
export interface BlogStats {
  totalPosts: number
  totalViews: number
  totalComments: number
  popularCategories: Array<{
    category: string
    count: number
    percentage: number
  }>
  topPosts: Array<{
    id: string
    title: string
    views: number
    slug: string
  }>
  recentActivity: Array<{
    type: 'post_published' | 'comment_added' | 'post_viewed'
    timestamp: Date
    data: any
  }>
}

// 内容质量评分
export interface ContentQualityScore {
  overall: number
  seo: {
    score: number
    issues: string[]
    suggestions: string[]
  }
  readability: {
    score: number
    level: 'easy' | 'medium' | 'hard'
    suggestions: string[]
  }
  engagement: {
    score: number
    factors: string[]
  }
}

// AI内容生成配置
export interface AIContentConfig {
  model: string
  temperature: number
  maxTokens: number
  prompt: {
    system: string
    user: string
  }
  postProcessing: {
    addTestRecommendations: boolean
    optimizeSEO: boolean
    generateImages: boolean
  }
}

// 博客主题配置
export interface BlogThemeConfig {
  typography: {
    fontFamily: {
      serif: string
      sans: string
      mono: string
    }
    fontSize: {
      base: string
      scale: number
    }
    lineHeight: {
      body: number
      heading: number
    }
  }
  colors: {
    primary: string
    secondary: string
    accent: string
    text: {
      primary: string
      secondary: string
      muted: string
    }
    background: {
      primary: string
      secondary: string
      accent: string
    }
  }
  spacing: {
    container: {
      maxWidth: string
      padding: string
    }
    content: {
      maxWidth: string
      lineHeight: number
    }
  }
}

// 博客导出/导入格式
export interface BlogExportData {
  version: string
  exportDate: Date
  posts: BlogPost[]
  categories: string[]
  tags: string[]
  metadata: {
    totalPosts: number
    languages: string[]
    dateRange: {
      start: Date
      end: Date
    }
  }
}

// 博客性能指标
export interface BlogPerformanceMetrics {
  pageLoadTime: number
  timeToFirstByte: number
  firstContentfulPaint: number
  largestContentfulPaint: number
  cumulativeLayoutShift: number
  firstInputDelay: number
  seoScore: number
  accessibilityScore: number
}

// 博客A/B测试配置
export interface BlogABTestConfig {
  testName: string
  variants: Array<{
    name: string
    weight: number
    config: {
      layout?: string
      ctaText?: string
      ctaColor?: string
      testRecommendations?: boolean
    }
  }>
  metrics: string[]
  duration: number
  targetAudience?: {
    locale?: string[]
    category?: string[]
    newVisitors?: boolean
  }
}

// 博客内容模板
export interface BlogContentTemplate {
  id: string
  name: string
  description: string
  category: BlogCategory
  structure: {
    sections: Array<{
      title: string
      type: 'text' | 'image' | 'quote' | 'list' | 'cta'
      required: boolean
      placeholder?: string
    }>
  }
  seoTemplate: {
    titlePattern: string
    descriptionPattern: string
    keywordSuggestions: string[]
  }
  testIntegration: {
    recommendedTests: TestType[]
    ctaPlacement: ('intro' | 'middle' | 'conclusion')[]
    ctaText: string
  }
}

// 博客内容审核状态
export interface BlogModerationStatus {
  status: 'pending' | 'approved' | 'rejected' | 'flagged'
  reviewer?: string
  reviewDate?: Date
  notes?: string
  flags?: Array<{
    type: 'inappropriate' | 'spam' | 'copyright' | 'quality'
    severity: 'low' | 'medium' | 'high'
    description: string
  }>
  autoModeration: {
    aiScore: number
    detectedIssues: string[]
    confidence: number
  }
}

// 博客社交分享配置
export interface BlogSocialShareConfig {
  platforms: Array<{
    name: 'twitter' | 'facebook' | 'linkedin' | 'pinterest' | 'whatsapp'
    enabled: boolean
    customText?: string
    hashtags?: string[]
  }>
  ogImage: {
    template: string
    width: number
    height: number
    quality: number
  }
  tracking: {
    utmSource: string
    utmMedium: string
    utmCampaign?: string
  }
}
