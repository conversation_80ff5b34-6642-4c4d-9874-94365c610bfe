import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { getTranslations } from 'next-intl/server';
import { Suspense } from 'react';
import { BlogService } from '@/lib/blog-service';
import { BLOG_CATEGORIES } from '@/types/blog';
import BlogList from '@/components/blog/BlogList';
import BlogHero from '@/components/blog/BlogHero';
import FeaturedPosts from '@/components/blog/FeaturedPosts';
import BlogCategories from '@/components/blog/BlogCategories';
import BlogSearch from '@/components/blog/BlogSearch';
import LoadingSpinner from '@/components/ui/LoadingSpinner';

interface BlogPageProps {
  params: {
    locale: string;
  };
  searchParams: {
    page?: string;
    category?: string;
    tag?: string;
    search?: string;
  };
}

export async function generateMetadata({ params }: BlogPageProps): Promise<Metadata> {
  const t = await getTranslations({ locale: params.locale, namespace: 'blog' });
  
  return {
    title: t('title'),
    description: t('description'),
    keywords: [
      t('keywords.mystical'),
      t('keywords.tarot'),
      t('keywords.astrology'),
      t('keywords.numerology'),
      t('keywords.crystal'),
      t('keywords.palmistry'),
      t('keywords.dreams'),
    ],
    openGraph: {
      title: t('title'),
      description: t('description'),
      type: 'website',
      locale: params.locale,
    },
  };
}

// 获取博客数据
async function getBlogData(
  locale: string,
  page: number = 1,
  limit: number = 12,
  filters?: {
    category?: string;
    tag?: string;
    search?: string;
  }
) {
  try {
    // 获取特色文章（浏览量最高的3篇）
    const featuredPostsResponse = await BlogService.getPosts({
      locale,
      status: 'PUBLISHED',
      limit: 3,
      sortBy: 'viewCount',
      sortOrder: 'desc'
    });

    // 获取常规文章列表
    const postsResponse = await BlogService.getPosts({
      locale,
      category: filters?.category,
      tags: filters?.tag ? [filters.tag] : undefined,
      search: filters?.search,
      status: 'PUBLISHED',
      page,
      limit,
      sortBy: 'publishedAt',
      sortOrder: 'desc'
    });

    return {
      posts: postsResponse.posts,
      featuredPosts: featuredPostsResponse.posts,
      totalPages: postsResponse.totalPages,
      total: postsResponse.total,
      categories: Object.values(BLOG_CATEGORIES)
    };
  } catch (error) {
    console.error('获取博客数据失败:', error);
    return {
      posts: [],
      featuredPosts: [],
      totalPages: 1,
      total: 0,
      categories: Object.values(BLOG_CATEGORIES)
    };
  }
}

export default async function BlogPage({ params, searchParams }: BlogPageProps) {
  const t = await getTranslations({ locale: params.locale, namespace: 'blog' });
  const locale = params.locale;

  const page = parseInt(searchParams.page || '1', 10);
  const { posts, featuredPosts, totalPages, total, categories } = await getBlogData(
    locale,
    page,
    12,
    {
      category: searchParams.category,
      tag: searchParams.tag,
      search: searchParams.search,
    }
  );

  // 结构化数据
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'Blog',
    name: 'Mystical Website Blog',
    description: 'Spiritual insights and guidance through mystical practices',
    url: `${process.env.NEXT_PUBLIC_APP_URL}/${locale}/blog`,
    publisher: {
      '@type': 'Organization',
      name: 'Mystical Website',
      logo: {
        '@type': 'ImageObject',
        url: `${process.env.NEXT_PUBLIC_APP_URL}/logo.png`
      }
    }
  };

  return (
    <>
      {/* 结构化数据 */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData)
        }}
      />

      <div className="min-h-screen bg-white dark:bg-dark-900">
        {/* 博客头部 */}
        <BlogHero locale={locale} />

        {/* 搜索和分类 */}
        <section className="max-w-7xl mx-auto px-4 py-8">
          <div className="flex flex-col lg:flex-row gap-6 items-center justify-between">
            <BlogCategories
              locale={locale}
              currentCategory={searchParams.category}
            />
            <BlogSearch
              locale={locale}
              initialSearch={searchParams.search}
            />
          </div>
        </section>

        {/* 特色文章（仅在首页显示） */}
        {!searchParams.category && !searchParams.tag && !searchParams.search && page === 1 && (
          <FeaturedPosts
            posts={featuredPosts}
            locale={locale}
          />
        )}

        {/* 文章列表 */}
        <Suspense fallback={<LoadingSpinner />}>
          <BlogList
            posts={posts}
            total={total}
            currentPage={page}
            totalPages={totalPages}
            locale={locale}
            searchParams={searchParams}
          />
        </Suspense>
      </div>
    </>
  );
}


