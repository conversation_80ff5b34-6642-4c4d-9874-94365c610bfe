'use client'

import Link from 'next/link'
import { useSearchParams } from 'next/navigation'
import { motion } from 'framer-motion'
import { 
  <PERSON><PERSON><PERSON>, 
  Star, 
  Zap, 
  Gem, 
  Eye, 
  Brain,
  Grid3X3
} from 'lucide-react'
import { BLOG_CATEGORIES } from '@/types/blog'

interface BlogCategoriesProps {
  locale: string
  currentCategory?: string
}

const categoryIcons = {
  tarot: Sparkles,
  astrology: Star,
  numerology: Zap,
  crystal: Gem,
  palmistry: Eye,
  dreams: Brain
}

export default function BlogCategories({ locale, currentCategory }: BlogCategoriesProps) {
  const searchParams = useSearchParams()
  
  // 创建新的搜索参数，保留其他参数但更新category
  const createCategoryUrl = (category?: string) => {
    const params = new URLSearchParams(searchParams.toString())
    if (category) {
      params.set('category', category)
    } else {
      params.delete('category')
    }
    params.delete('page') // 重置页码
    
    const queryString = params.toString()
    return `/${locale}/blog${queryString ? `?${queryString}` : ''}`
  }
  
  return (
    <div className="flex flex-wrap gap-3 items-center">
      {/* 全部分类 */}
      <Link href={createCategoryUrl()}>
        <motion.div
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          className={`flex items-center space-x-2 px-4 py-2 rounded-full border transition-all duration-200 ${
            !currentCategory
              ? 'bg-mystical-500 text-white border-mystical-500 shadow-lg'
              : 'bg-white dark:bg-dark-800 text-mystical-600 dark:text-mystical-400 border-mystical-200 dark:border-dark-600 hover:border-mystical-300 dark:hover:border-dark-500 hover:bg-mystical-50 dark:hover:bg-dark-700'
          }`}
        >
          <Grid3X3 className="w-4 h-4" />
          <span className="text-sm font-medium">All Categories</span>
        </motion.div>
      </Link>
      
      {/* 分类列表 */}
      {Object.entries(BLOG_CATEGORIES).map(([key, category]) => {
        const Icon = categoryIcons[key as keyof typeof categoryIcons]
        const isActive = currentCategory === key
        
        return (
          <Link key={key} href={createCategoryUrl(key)}>
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className={`flex items-center space-x-2 px-4 py-2 rounded-full border transition-all duration-200 ${
                isActive
                  ? `bg-${category.color} text-white border-${category.color} shadow-lg`
                  : `bg-white dark:bg-dark-800 text-mystical-600 dark:text-mystical-400 border-mystical-200 dark:border-dark-600 hover:border-${category.color}/50 hover:bg-${category.color}/10 dark:hover:bg-dark-700`
              }`}
            >
              <Icon className="w-4 h-4" />
              <span className="text-sm font-medium">{category.name}</span>
            </motion.div>
          </Link>
        )
      })}
    </div>
  )
}
