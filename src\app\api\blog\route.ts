import { NextRequest, NextResponse } from 'next/server'
import { BlogService } from '@/lib/blog-service'
import type { CreateBlogPostData, BlogListParams } from '@/lib/blog-service'

/**
 * 获取博客文章列表
 * GET /api/blog
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    const params: BlogListParams = {
      locale: searchParams.get('locale') || 'en',
      category: searchParams.get('category') || undefined,
      status: (searchParams.get('status') as any) || 'PUBLISHED',
      tags: searchParams.get('tags')?.split(',') || undefined,
      search: searchParams.get('search') || undefined,
      page: parseInt(searchParams.get('page') || '1'),
      limit: parseInt(searchParams.get('limit') || '10'),
      sortBy: (searchParams.get('sortBy') as any) || 'publishedAt',
      sortOrder: (searchParams.get('sortOrder') as any) || 'desc'
    }
    
    const result = await BlogService.getPosts(params)
    
    return NextResponse.json({
      success: true,
      data: result
    })
    
  } catch (error) {
    console.error('获取博客列表失败:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch blog posts',
        message: error.message
      },
      { status: 500 }
    )
  }
}

/**
 * 创建博客文章
 * POST /api/blog
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // 验证必需字段
    const requiredFields = ['title', 'content', 'locale', 'category']
    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json(
          {
            success: false,
            error: `Missing required field: ${field}`
          },
          { status: 400 }
        )
      }
    }
    
    const postData: CreateBlogPostData = {
      title: body.title,
      slug: body.slug,
      content: body.content,
      excerpt: body.excerpt,
      coverImage: body.coverImage,
      locale: body.locale,
      category: body.category,
      tags: body.tags || [],
      status: body.status || 'DRAFT',
      publishedAt: body.publishedAt ? new Date(body.publishedAt) : undefined,
      seoTitle: body.seoTitle,
      seoDescription: body.seoDescription,
      keywords: body.keywords,
      metadata: body.metadata
    }
    
    const post = await BlogService.createPost(postData)
    
    return NextResponse.json({
      success: true,
      data: post
    }, { status: 201 })
    
  } catch (error) {
    console.error('创建博客文章失败:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to create blog post',
        message: error.message
      },
      { status: 500 }
    )
  }
}
