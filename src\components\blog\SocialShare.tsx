'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Share2, 
  Twitter, 
  Facebook, 
  Linkedin, 
  Link2, 
  MessageCircle,
  Check
} from 'lucide-react'
import type { BlogPost } from '@prisma/client'
import { generateSocialShareLinks } from '@/lib/blog-utils'

interface SocialShareProps {
  post: BlogPost
  locale: string
  className?: string
}

export default function SocialShare({ post, locale, className = '' }: SocialShareProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [copied, setCopied] = useState(false)
  
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://mystical-website.com'
  const shareLinks = generateSocialShareLinks(post, baseUrl)
  const postUrl = `${baseUrl}/${locale}/blog/${post.slug}`
  
  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(postUrl)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      console.error('复制链接失败:', error)
    }
  }
  
  const shareOptions = [
    {
      name: 'Twitter',
      icon: Twitter,
      url: shareLinks.twitter,
      color: 'hover:text-blue-500'
    },
    {
      name: 'Facebook',
      icon: Facebook,
      url: shareLinks.facebook,
      color: 'hover:text-blue-600'
    },
    {
      name: 'LinkedIn',
      icon: Linkedin,
      url: shareLinks.linkedin,
      color: 'hover:text-blue-700'
    },
    {
      name: 'WhatsApp',
      icon: MessageCircle,
      url: shareLinks.whatsapp,
      color: 'hover:text-green-500'
    }
  ]
  
  return (
    <div className={`relative ${className}`}>
      {/* 分享按钮 */}
      <motion.button
        className="flex items-center space-x-2 px-4 py-2 text-mystical-600 dark:text-mystical-400 hover:text-mystical-700 dark:hover:text-mystical-300 transition-colors"
        onClick={() => setIsOpen(!isOpen)}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
      >
        <Share2 className="w-5 h-5" />
        <span className="text-sm font-medium">Share</span>
      </motion.button>
      
      {/* 分享菜单 */}
      <AnimatePresence>
        {isOpen && (
          <>
            {/* 背景遮罩 */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 z-40"
              onClick={() => setIsOpen(false)}
            />
            
            {/* 分享选项 */}
            <motion.div
              initial={{ opacity: 0, scale: 0.95, y: -10 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.95, y: -10 }}
              className="absolute top-full right-0 mt-2 w-64 bg-white dark:bg-dark-800 rounded-xl shadow-xl border border-mystical-200 dark:border-dark-600 z-50 overflow-hidden"
            >
              {/* 标题 */}
              <div className="px-4 py-3 border-b border-mystical-200 dark:border-dark-600">
                <h3 className="text-sm font-semibold text-mystical-900 dark:text-white">
                  Share this article
                </h3>
              </div>
              
              {/* 社交平台选项 */}
              <div className="py-2">
                {shareOptions.map((option) => (
                  <a
                    key={option.name}
                    href={option.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className={`flex items-center space-x-3 px-4 py-3 text-mystical-700 dark:text-mystical-300 hover:bg-mystical-50 dark:hover:bg-dark-700 transition-colors ${option.color}`}
                    onClick={() => setIsOpen(false)}
                  >
                    <option.icon className="w-5 h-5" />
                    <span className="text-sm font-medium">Share on {option.name}</span>
                  </a>
                ))}
                
                {/* 复制链接 */}
                <button
                  onClick={handleCopyLink}
                  className="flex items-center space-x-3 w-full px-4 py-3 text-mystical-700 dark:text-mystical-300 hover:bg-mystical-50 dark:hover:bg-dark-700 transition-colors"
                >
                  {copied ? (
                    <Check className="w-5 h-5 text-green-500" />
                  ) : (
                    <Link2 className="w-5 h-5" />
                  )}
                  <span className="text-sm font-medium">
                    {copied ? 'Link copied!' : 'Copy link'}
                  </span>
                </button>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </div>
  )
}
