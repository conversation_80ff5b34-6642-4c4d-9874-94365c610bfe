import { BlogService } from '@/lib/blog-service'
import { AIContentGenerator } from '@/lib/ai-content-generator'
import { TestIntegrationService } from '@/lib/test-integration'

/**
 * 测试博客系统的各个功能
 */
export async function testBlogSystem() {
  console.log('🧪 开始测试博客系统...\n')
  
  const testResults = {
    blogService: false,
    aiGenerator: false,
    testIntegration: false,
    multiLanguage: false
  }
  
  try {
    // 1. 测试博客服务基本功能
    console.log('1️⃣ 测试博客服务基本功能...')
    await testBlogServiceBasics()
    testResults.blogService = true
    console.log('✅ 博客服务测试通过\n')
    
    // 2. 测试AI内容生成器
    console.log('2️⃣ 测试AI内容生成器...')
    await testAIContentGenerator()
    testResults.aiGenerator = true
    console.log('✅ AI内容生成器测试通过\n')
    
    // 3. 测试测试集成功能
    console.log('3️⃣ 测试测试集成功能...')
    await testTestIntegration()
    testResults.testIntegration = true
    console.log('✅ 测试集成功能测试通过\n')
    
    // 4. 测试多语言支持
    console.log('4️⃣ 测试多语言支持...')
    await testMultiLanguageSupport()
    testResults.multiLanguage = true
    console.log('✅ 多语言支持测试通过\n')
    
  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error)
  }
  
  // 输出测试结果
  console.log('📊 测试结果总结:')
  console.log(`博客服务: ${testResults.blogService ? '✅ 通过' : '❌ 失败'}`)
  console.log(`AI生成器: ${testResults.aiGenerator ? '✅ 通过' : '❌ 失败'}`)
  console.log(`测试集成: ${testResults.testIntegration ? '✅ 通过' : '❌ 失败'}`)
  console.log(`多语言支持: ${testResults.multiLanguage ? '✅ 通过' : '❌ 失败'}`)
  
  const passedTests = Object.values(testResults).filter(Boolean).length
  const totalTests = Object.keys(testResults).length
  
  console.log(`\n🎯 总体结果: ${passedTests}/${totalTests} 项测试通过`)
  
  return testResults
}

/**
 * 测试博客服务基本功能
 */
async function testBlogServiceBasics() {
  // 测试获取博客列表
  const posts = await BlogService.getPosts({
    locale: 'en',
    limit: 5
  })
  
  console.log(`   📝 获取到 ${posts.posts.length} 篇文章`)
  
  // 测试分类筛选
  const tarotPosts = await BlogService.getPosts({
    locale: 'en',
    category: 'tarot',
    limit: 3
  })
  
  console.log(`   🔮 塔罗分类文章: ${tarotPosts.posts.length} 篇`)
  
  // 测试搜索功能
  const searchResults = await BlogService.getPosts({
    locale: 'en',
    search: 'tarot',
    limit: 3
  })
  
  console.log(`   🔍 搜索结果: ${searchResults.posts.length} 篇`)
}

/**
 * 测试AI内容生成器
 */
async function testAIContentGenerator() {
  const generator = new AIContentGenerator()
  
  // 测试获取推荐主题
  const topics = AIContentGenerator.getRecommendedTopics('tarot')
  console.log(`   💡 塔罗推荐主题: ${topics.length} 个`)
  
  // 测试单篇文章生成（模拟）
  try {
    const article = await generator.generateArticle({
      topic: 'The Fool Card Meaning',
      category: 'tarot',
      locale: 'en',
      length: 'medium'
    })
    
    console.log(`   📄 生成文章: "${article.title}"`)
    console.log(`   📊 内容长度: ${article.content.length} 字符`)
    console.log(`   🏷️ 标签数量: ${article.tags.length} 个`)
    
  } catch (error) {
    console.log(`   ⚠️ AI生成测试（预期行为）: ${error.message}`)
  }
}

/**
 * 测试测试集成功能
 */
async function testTestIntegration() {
  const sampleContent = `
    <h1>Understanding Tarot Cards</h1>
    <p>Tarot cards have been used for divination and guidance for centuries...</p>
    <p>The Major Arcana represents major life events and spiritual lessons...</p>
    <p>Learning to read tarot requires practice and intuition...</p>
  `
  
  // 测试获取测试推荐
  const recommendations = TestIntegrationService.getTestRecommendations(
    'tarot',
    sampleContent,
    'Understanding Tarot Cards',
    ['tarot', 'divination', 'spirituality']
  )
  
  console.log(`   🎯 获得 ${recommendations.length} 个测试推荐`)
  
  // 测试生成CTA
  if (recommendations.length > 0) {
    const cta = TestIntegrationService.generateTestCTA(
      recommendations[0],
      'conclusion',
      'en'
    )
    
    console.log(`   🔗 生成CTA长度: ${cta.html.length} 字符`)
  }
  
  // 测试内容增强
  const enhancedContent = TestIntegrationService.insertTestCTAs(
    sampleContent,
    recommendations,
    'en'
  )
  
  const enhancement = enhancedContent.length - sampleContent.length
  console.log(`   ✨ 内容增强: +${enhancement} 字符`)
}

/**
 * 测试多语言支持
 */
async function testMultiLanguageSupport() {
  const locales = ['en', 'zh-CN', 'zh-TW']
  
  for (const locale of locales) {
    const posts = await BlogService.getPosts({
      locale,
      limit: 3
    })
    
    console.log(`   🌍 ${locale}: ${posts.posts.length} 篇文章`)
  }
  
  // 测试多语言测试集成
  const recommendations = TestIntegrationService.getTestRecommendations(
    'tarot',
    'Sample content about tarot cards',
    'Tarot Guide',
    ['tarot']
  )
  
  if (recommendations.length > 0) {
    const enCTA = TestIntegrationService.generateTestCTA(recommendations[0], 'conclusion', 'en')
    const cnCTA = TestIntegrationService.generateTestCTA(recommendations[0], 'conclusion', 'zh-CN')
    
    console.log(`   🔗 英文CTA: ${enCTA.html.includes('Get Your') ? '✅' : '❌'}`)
    console.log(`   🔗 中文CTA: ${cnCTA.html.includes('获取您的') ? '✅' : '❌'}`)
  }
}

/**
 * 性能测试
 */
export async function performanceTest() {
  console.log('⚡ 开始性能测试...\n')
  
  const startTime = Date.now()
  
  // 测试并发获取文章
  const promises = []
  for (let i = 0; i < 5; i++) {
    promises.push(BlogService.getPosts({
      locale: 'en',
      limit: 10
    }))
  }
  
  await Promise.all(promises)
  
  const endTime = Date.now()
  const duration = endTime - startTime
  
  console.log(`📊 并发获取5次文章列表耗时: ${duration}ms`)
  console.log(`⚡ 平均响应时间: ${duration / 5}ms`)
  
  return { duration, averageTime: duration / 5 }
}

// 如果直接运行此脚本
if (require.main === module) {
  testBlogSystem()
    .then(async () => {
      console.log('\n⚡ 运行性能测试...')
      await performanceTest()
      console.log('\n🎉 所有测试完成！')
      process.exit(0)
    })
    .catch((error) => {
      console.error('💥 测试过程中出错:', error)
      process.exit(1)
    })
}
