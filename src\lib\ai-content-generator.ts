import type { AIGeneratedPost } from './blog-service'
import type { TestType } from '@prisma/client'

// AI服务配置
interface AIServiceConfig {
  name: string
  apiKey: string
  endpoint: string
  model: string
  maxTokens: number
  temperature: number
}

// AI服务提供商
const AI_SERVICES: Record<string, AIServiceConfig> = {
  qwen: {
    name: '<PERSON><PERSON> (通义千问)',
    apiKey: process.env.QWEN_API_KEY || '',
    endpoint: 'https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation',
    model: 'qwen-turbo',
    maxTokens: 2000,
    temperature: 0.7
  },
  doubao: {
    name: '<PERSON><PERSON><PERSON> (豆包)',
    apiKey: process.env.DOUBAO_API_KEY || '',
    endpoint: 'https://ark.cn-beijing.volces.com/api/v3/chat/completions',
    model: 'doubao-lite-4k',
    maxTokens: 2000,
    temperature: 0.7
  },
  zhipu: {
    name: '<PERSON><PERSON><PERSON><PERSON><PERSON> (智谱AI)',
    apiKey: process.env.ZHIPU_API_KEY || '',
    endpoint: 'https://open.bigmodel.cn/api/paas/v4/chat/completions',
    model: 'glm-4',
    maxTokens: 2000,
    temperature: 0.7
  }
}

// 内容生成模板
const CONTENT_TEMPLATES = {
  tarot: {
    systemPrompt: `你是一位专业的塔罗牌解读师和内容创作者。请创作高质量的塔罗相关文章，内容要准确、有深度、易于理解。`,
    categories: [
      'tarot-basics', 'card-meanings', 'spreads', 'interpretation', 
      'history', 'symbolism', 'daily-guidance', 'love-readings'
    ],
    testIntegration: true
  },
  astrology: {
    systemPrompt: `你是一位专业的占星师和内容创作者。请创作高质量的占星学文章，内容要科学准确、有深度、实用性强。`,
    categories: [
      'zodiac-signs', 'planets', 'houses', 'aspects', 
      'birth-chart', 'compatibility', 'transits', 'horoscope'
    ],
    testIntegration: true
  },
  numerology: {
    systemPrompt: `你是一位专业的数字命理师和内容创作者。请创作高质量的数字命理文章，内容要准确、有逻辑、实用性强。`,
    categories: [
      'life-path', 'expression-number', 'soul-urge', 'personality-number',
      'master-numbers', 'karmic-numbers', 'calculations', 'meanings'
    ],
    testIntegration: true
  },
  crystal: {
    systemPrompt: `你是一位专业的水晶治疗师和内容创作者。请创作高质量的水晶能量文章，内容要准确、实用、有科学依据。`,
    categories: [
      'crystal-types', 'healing-properties', 'chakras', 'meditation',
      'cleansing', 'programming', 'combinations', 'daily-use'
    ],
    testIntegration: true
  },
  palmistry: {
    systemPrompt: `你是一位专业的手相师和内容创作者。请创作高质量的手相学文章，内容要准确、详细、易于理解。`,
    categories: [
      'palm-lines', 'hand-shapes', 'fingers', 'mounts',
      'markings', 'interpretation', 'compatibility', 'career-guidance'
    ],
    testIntegration: true
  },
  dreams: {
    systemPrompt: `你是一位专业的梦境解析师和内容创作者。请创作高质量的梦境解析文章，内容要准确、有深度、实用性强。`,
    categories: [
      'dream-symbols', 'interpretation', 'lucid-dreaming', 'nightmares',
      'recurring-dreams', 'prophetic-dreams', 'dream-journal', 'meanings'
    ],
    testIntegration: true
  }
}

/**
 * AI内容生成器类
 */
export class AIContentGenerator {
  private currentService: string = 'qwen'
  private fallbackServices: string[] = ['doubao', 'zhipu']
  
  /**
   * 生成单篇文章
   */
  async generateArticle(params: {
    topic: string
    category: keyof typeof CONTENT_TEMPLATES
    locale: string
    length: 'short' | 'medium' | 'long'
    includeTestRecommendation?: boolean
    customPrompt?: string
  }): Promise<AIGeneratedPost> {
    const { topic, category, locale, length, includeTestRecommendation = true, customPrompt } = params
    
    try {
      const template = CONTENT_TEMPLATES[category]
      const prompt = this.buildPrompt({
        topic,
        category,
        locale,
        length,
        includeTestRecommendation,
        systemPrompt: template.systemPrompt,
        customPrompt
      })
      
      const content = await this.callAIService(prompt)
      const parsedContent = this.parseAIResponse(content)
      
      return {
        title: parsedContent.title,
        content: parsedContent.content,
        excerpt: parsedContent.excerpt,
        locale,
        category,
        tags: parsedContent.tags,
        seoTitle: parsedContent.seoTitle,
        seoDescription: parsedContent.seoDescription,
        keywords: parsedContent.keywords,
        coverImage: await this.generateCoverImage(parsedContent.title, category),
        aiMetadata: {
          model: AI_SERVICES[this.currentService].model,
          generatedAt: new Date(),
          prompt: prompt.substring(0, 200) + '...',
          confidence: 0.85
        }
      }
      
    } catch (error) {
      console.error('AI内容生成失败:', error)
      throw new Error(`Failed to generate article: ${error.message}`)
    }
  }
  
  /**
   * 批量生成文章
   */
  async batchGenerateArticles(topics: Array<{
    topic: string
    category: keyof typeof CONTENT_TEMPLATES
    locale: string
    length?: 'short' | 'medium' | 'long'
  }>): Promise<{
    successful: AIGeneratedPost[]
    failed: Array<{ topic: string; error: string }>
    total: number
  }> {
    const successful: AIGeneratedPost[] = []
    const failed: Array<{ topic: string; error: string }> = []
    
    for (const topicData of topics) {
      try {
        const article = await this.generateArticle({
          ...topicData,
          length: topicData.length || 'medium'
        })
        successful.push(article)
        
        // 添加延迟避免API限制
        await this.delay(1000)
        
      } catch (error) {
        failed.push({
          topic: topicData.topic,
          error: error.message
        })
      }
    }
    
    return {
      successful,
      failed,
      total: topics.length
    }
  }
  
  /**
   * 构建AI提示词
   */
  private buildPrompt(params: {
    topic: string
    category: string
    locale: string
    length: string
    includeTestRecommendation: boolean
    systemPrompt: string
    customPrompt?: string
  }): string {
    const { topic, category, locale, length, includeTestRecommendation, systemPrompt, customPrompt } = params
    
    const lengthGuide = {
      short: '800-1200字',
      medium: '1500-2500字',
      long: '3000-5000字'
    }
    
    const testRecommendationText = includeTestRecommendation 
      ? `\n\n在文章结尾，自然地推荐相关的${category}测试，鼓励读者进行免费测试以获得个性化分析。`
      : ''
    
    return `${systemPrompt}

请根据以下要求创作一篇关于"${topic}"的${category}文章：

**基本要求：**
- 语言：${locale === 'zh-CN' ? '简体中文' : locale === 'zh-TW' ? '繁体中文' : '英语'}
- 长度：${lengthGuide[length as keyof typeof lengthGuide]}
- 目标读者：对${category}感兴趣的初学者和进阶者

**内容结构：**
1. 引人入胜的开头
2. 主要内容（分为3-5个小节）
3. 实用建议或指导
4. 总结和启发${testRecommendationText}

**SEO要求：**
- 标题要吸引人且包含关键词
- 包含相关的长尾关键词
- 内容要有价值和深度

**输出格式：**
请严格按照以下JSON格式输出：
{
  "title": "文章标题",
  "content": "完整的HTML格式文章内容",
  "excerpt": "150字以内的文章摘要",
  "tags": ["标签1", "标签2", "标签3", "标签4", "标签5"],
  "seoTitle": "SEO优化的标题（60字以内）",
  "seoDescription": "SEO描述（160字以内）",
  "keywords": ["关键词1", "关键词2", "关键词3", "关键词4", "关键词5"]
}

${customPrompt ? `\n**额外要求：**\n${customPrompt}` : ''}

请开始创作：`
  }
  
  /**
   * 调用AI服务
   */
  private async callAIService(prompt: string): Promise<string> {
    const services = [this.currentService, ...this.fallbackServices]
    
    for (const serviceName of services) {
      try {
        const service = AI_SERVICES[serviceName]
        if (!service.apiKey) {
          console.warn(`${service.name} API密钥未配置，跳过`)
          continue
        }
        
        const response = await this.makeAPICall(service, prompt)
        this.currentService = serviceName // 更新当前服务
        return response
        
      } catch (error) {
        console.error(`${serviceName} 服务调用失败:`, error)
        continue
      }
    }
    
    throw new Error('所有AI服务都不可用')
  }
  
  /**
   * 实际的API调用
   */
  private async makeAPICall(service: AIServiceConfig, prompt: string): Promise<string> {
    // 这里是模拟实现，实际项目中需要根据不同的AI服务实现具体的API调用
    // 由于涉及到具体的API密钥和调用方式，这里返回模拟数据
    
    await this.delay(2000) // 模拟API调用延迟
    
    return JSON.stringify({
      title: `Understanding ${prompt.split('"')[1]} - A Comprehensive Guide`,
      content: `<h1>Understanding ${prompt.split('"')[1]}</h1>
        <p>This is a comprehensive guide about ${prompt.split('"')[1]} that provides deep insights and practical guidance.</p>
        <h2>Introduction</h2>
        <p>In the mystical world of divination and spiritual guidance, understanding ${prompt.split('"')[1]} is essential for personal growth and enlightenment.</p>
        <h2>Key Concepts</h2>
        <p>Let's explore the fundamental concepts that make ${prompt.split('"')[1]} such a powerful tool for self-discovery.</p>
        <h2>Practical Applications</h2>
        <p>Here are some practical ways to apply this knowledge in your daily life.</p>
        <h2>Conclusion</h2>
        <p>By understanding ${prompt.split('"')[1]}, you can unlock new levels of spiritual awareness and personal insight.</p>`,
      excerpt: `Discover the profound wisdom of ${prompt.split('"')[1]} and learn how to apply these ancient insights to your modern life for greater clarity and spiritual growth.`,
      tags: ['spirituality', 'guidance', 'mystical', 'wisdom', 'personal-growth'],
      seoTitle: `${prompt.split('"')[1]} Guide - Spiritual Insights & Wisdom`,
      seoDescription: `Learn about ${prompt.split('"')[1]} with our comprehensive guide. Discover spiritual insights, practical applications, and ancient wisdom for modern life.`,
      keywords: ['spiritual guidance', 'mystical wisdom', 'personal growth', 'divination', 'enlightenment']
    })
  }
  
  /**
   * 解析AI响应
   */
  private parseAIResponse(response: string): {
    title: string
    content: string
    excerpt: string
    tags: string[]
    seoTitle: string
    seoDescription: string
    keywords: string[]
  } {
    try {
      const parsed = JSON.parse(response)
      
      // 验证必需字段
      if (!parsed.title || !parsed.content || !parsed.excerpt) {
        throw new Error('AI响应缺少必需字段')
      }
      
      return {
        title: parsed.title,
        content: parsed.content,
        excerpt: parsed.excerpt,
        tags: parsed.tags || [],
        seoTitle: parsed.seoTitle || parsed.title,
        seoDescription: parsed.seoDescription || parsed.excerpt,
        keywords: parsed.keywords || parsed.tags || []
      }
      
    } catch (error) {
      console.error('解析AI响应失败:', error)
      throw new Error('AI响应格式无效')
    }
  }
  
  /**
   * 生成封面图片（可选功能）
   */
  private async generateCoverImage(title: string, category: string): Promise<string | undefined> {
    // 这里可以集成图片生成服务，如DALL-E、Midjourney等
    // 暂时返回undefined，使用默认图片
    return undefined
  }
  
  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
  
  /**
   * 获取推荐主题
   */
  static getRecommendedTopics(category: keyof typeof CONTENT_TEMPLATES): string[] {
    const topicSuggestions = {
      tarot: [
        'The Fool\'s Journey in Tarot',
        'Understanding Major Arcana Cards',
        'Celtic Cross Spread Guide',
        'Tarot for Daily Guidance',
        'Love and Relationships in Tarot',
        'Career Guidance Through Tarot',
        'Tarot Card Combinations',
        'Reversed Cards Meanings'
      ],
      astrology: [
        'Your Sun Sign Personality',
        'Moon Signs and Emotions',
        'Rising Sign First Impressions',
        'Mercury Retrograde Effects',
        'Venus in Love and Relationships',
        'Mars and Your Drive',
        'Jupiter\'s Blessings',
        'Saturn\'s Life Lessons'
      ],
      numerology: [
        'Life Path Number Meanings',
        'Expression Number Guide',
        'Soul Urge Number Insights',
        'Master Numbers 11, 22, 33',
        'Personal Year Cycles',
        'Compatibility Through Numbers',
        'Name Numerology',
        'Birth Date Analysis'
      ],
      crystal: [
        'Beginner\'s Guide to Crystals',
        'Chakra Healing Crystals',
        'Crystal Meditation Techniques',
        'Cleansing and Charging Crystals',
        'Crystal Combinations for Love',
        'Protective Crystal Shields',
        'Crystal Grids for Manifestation',
        'Daily Crystal Practices'
      ],
      palmistry: [
        'Reading the Life Line',
        'Heart Line Interpretations',
        'Head Line Analysis',
        'Fate Line Meanings',
        'Hand Shapes and Personality',
        'Finger Length Significance',
        'Palm Mounts Explained',
        'Marriage Lines in Palmistry'
      ],
      dreams: [
        'Common Dream Symbols',
        'Lucid Dreaming Techniques',
        'Nightmare Meanings',
        'Recurring Dreams Analysis',
        'Prophetic Dreams Guide',
        'Dream Journal Benefits',
        'Animal Dreams Interpretation',
        'Flying Dreams Significance'
      ]
    }
    
    return topicSuggestions[category] || []
  }
}
