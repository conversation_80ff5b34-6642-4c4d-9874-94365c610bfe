'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { Heart, Bookmark, MessageCircle, Share2 } from 'lucide-react'

interface InteractionButtonsProps {
  postId: string
  initialLikes: number
  initialBookmarks: number
  isLiked: boolean
  isBookmarked: boolean
  onShare?: () => void
}

export default function InteractionButtons({
  postId,
  initialLikes,
  initialBookmarks,
  isLiked: initialIsLiked,
  isBookmarked: initialIsBookmarked,
  onShare
}: InteractionButtonsProps) {
  const [likes, setLikes] = useState(initialLikes)
  const [bookmarks, setBookmarks] = useState(initialBookmarks)
  const [isLiked, setIsLiked] = useState(initialIsLiked)
  const [isBookmarked, setIsBookmarked] = useState(initialIsBookmarked)
  const [isLoading, setIsLoading] = useState<string | null>(null)
  
  const handleLike = async () => {
    if (isLoading) return
    
    setIsLoading('like')
    
    try {
      // 乐观更新
      const newIsLiked = !isLiked
      const newLikes = newIsLiked ? likes + 1 : likes - 1
      
      setIsLiked(newIsLiked)
      setLikes(newLikes)
      
      // 这里应该调用API更新后端
      // await updatePostLike(postId, newIsLiked)
      
    } catch (error) {
      // 回滚状态
      setIsLiked(!isLiked)
      setLikes(isLiked ? likes - 1 : likes + 1)
      console.error('点赞失败:', error)
    } finally {
      setIsLoading(null)
    }
  }
  
  const handleBookmark = async () => {
    if (isLoading) return
    
    setIsLoading('bookmark')
    
    try {
      // 乐观更新
      const newIsBookmarked = !isBookmarked
      const newBookmarks = newIsBookmarked ? bookmarks + 1 : bookmarks - 1
      
      setIsBookmarked(newIsBookmarked)
      setBookmarks(newBookmarks)
      
      // 这里应该调用API更新后端
      // await updatePostBookmark(postId, newIsBookmarked)
      
    } catch (error) {
      // 回滚状态
      setIsBookmarked(!isBookmarked)
      setBookmarks(isBookmarked ? bookmarks - 1 : bookmarks + 1)
      console.error('收藏失败:', error)
    } finally {
      setIsLoading(null)
    }
  }
  
  const buttonVariants = {
    hover: { scale: 1.1 },
    tap: { scale: 0.95 }
  }
  
  const iconVariants = {
    liked: { scale: [1, 1.3, 1], transition: { duration: 0.3 } },
    bookmarked: { scale: [1, 1.3, 1], transition: { duration: 0.3 } }
  }
  
  return (
    <div className="flex flex-col space-y-4">
      {/* 点赞按钮 */}
      <div className="flex flex-col items-center">
        <motion.button
          className={`w-12 h-12 rounded-full flex items-center justify-center transition-all duration-200 ${
            isLiked
              ? 'bg-red-500 text-white shadow-lg'
              : 'bg-white dark:bg-dark-800 text-mystical-600 dark:text-mystical-400 border border-mystical-200 dark:border-dark-600 hover:border-red-300 hover:text-red-500'
          }`}
          onClick={handleLike}
          disabled={isLoading === 'like'}
          variants={buttonVariants}
          whileHover="hover"
          whileTap="tap"
        >
          <motion.div
            variants={iconVariants}
            animate={isLiked ? 'liked' : 'initial'}
          >
            <Heart 
              className={`w-5 h-5 ${isLiked ? 'fill-current' : ''}`}
            />
          </motion.div>
        </motion.button>
        
        {likes > 0 && (
          <span className="text-xs text-mystical-500 dark:text-mystical-400 mt-1">
            {likes}
          </span>
        )}
      </div>
      
      {/* 收藏按钮 */}
      <div className="flex flex-col items-center">
        <motion.button
          className={`w-12 h-12 rounded-full flex items-center justify-center transition-all duration-200 ${
            isBookmarked
              ? 'bg-mystical-500 text-white shadow-lg'
              : 'bg-white dark:bg-dark-800 text-mystical-600 dark:text-mystical-400 border border-mystical-200 dark:border-dark-600 hover:border-mystical-300 hover:text-mystical-500'
          }`}
          onClick={handleBookmark}
          disabled={isLoading === 'bookmark'}
          variants={buttonVariants}
          whileHover="hover"
          whileTap="tap"
        >
          <motion.div
            variants={iconVariants}
            animate={isBookmarked ? 'bookmarked' : 'initial'}
          >
            <Bookmark 
              className={`w-5 h-5 ${isBookmarked ? 'fill-current' : ''}`}
            />
          </motion.div>
        </motion.button>
        
        {bookmarks > 0 && (
          <span className="text-xs text-mystical-500 dark:text-mystical-400 mt-1">
            {bookmarks}
          </span>
        )}
      </div>
      
      {/* 评论按钮 */}
      <div className="flex flex-col items-center">
        <motion.button
          className="w-12 h-12 rounded-full bg-white dark:bg-dark-800 text-mystical-600 dark:text-mystical-400 border border-mystical-200 dark:border-dark-600 hover:border-mystical-300 hover:text-mystical-500 flex items-center justify-center transition-all duration-200"
          onClick={() => {
            // 滚动到评论区域
            const commentsSection = document.getElementById('comments')
            if (commentsSection) {
              commentsSection.scrollIntoView({ behavior: 'smooth' })
            }
          }}
          variants={buttonVariants}
          whileHover="hover"
          whileTap="tap"
        >
          <MessageCircle className="w-5 h-5" />
        </motion.button>
      </div>
      
      {/* 分享按钮 */}
      <div className="flex flex-col items-center">
        <motion.button
          className="w-12 h-12 rounded-full bg-white dark:bg-dark-800 text-mystical-600 dark:text-mystical-400 border border-mystical-200 dark:border-dark-600 hover:border-mystical-300 hover:text-mystical-500 flex items-center justify-center transition-all duration-200"
          onClick={onShare}
          variants={buttonVariants}
          whileHover="hover"
          whileTap="tap"
        >
          <Share2 className="w-5 h-5" />
        </motion.button>
      </div>
    </div>
  )
}
