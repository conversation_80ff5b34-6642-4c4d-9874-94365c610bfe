# 博客系统使用指南

## 概述

本博客系统是一个专为玄学网站设计的现代化内容管理系统，具有以下核心特性：

- 🌍 **多语言支持** - 支持英语、简体中文、繁体中文等多种语言
- 🤖 **AI内容生成** - 集成多个AI服务自动生成高质量内容
- 🎯 **智能测试导流** - 自动在文章中插入相关测试推荐
- 📱 **响应式设计** - 完美适配桌面端和移动端
- ⚡ **高性能** - 基于Next.js和Supabase的现代技术栈

## 技术架构

### 核心组件

1. **BlogService** - 博客数据管理服务
2. **AIContentGenerator** - AI内容生成器
3. **TestIntegrationService** - 测试导流集成服务
4. **多语言组件** - 支持多语言的UI组件

### 数据库设计

```sql
-- 博客文章表
CREATE TABLE blog_posts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title TEXT NOT NULL,
  slug TEXT NOT NULL,
  content TEXT NOT NULL,
  excerpt TEXT,
  cover_image TEXT,
  locale TEXT NOT NULL,
  category TEXT NOT NULL,
  tags TEXT[] DEFAULT '{}',
  status post_status DEFAULT 'DRAFT',
  published_at TIMESTAMPTZ,
  reading_time INTEGER DEFAULT 0,
  view_count INTEGER DEFAULT 0,
  seo_title TEXT,
  seo_description TEXT,
  keywords TEXT[],
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  UNIQUE(slug, locale)
);
```

## 快速开始

### 1. 环境配置

确保以下环境变量已配置：

```env
# Supabase配置
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key

# AI服务配置（可选）
QWEN_API_KEY=your_qwen_api_key
DOUBAO_API_KEY=your_doubao_api_key
ZHIPU_API_KEY=your_zhipu_api_key
```

### 2. 创建示例内容

```bash
# 运行示例博文创建脚本
npm run create-sample-posts

# 或者使用TypeScript直接运行
npx tsx src/scripts/create-sample-posts.ts
```

### 3. 测试系统功能

```bash
# 运行系统测试
npm run test-blog-system

# 或者使用TypeScript直接运行
npx tsx src/scripts/test-blog-system.ts
```

## API使用指南

### 获取博客文章列表

```typescript
import { BlogService } from '@/lib/blog-service'

// 获取英文文章
const posts = await BlogService.getPosts({
  locale: 'en',
  category: 'tarot',
  limit: 10,
  page: 1
})

// 搜索文章
const searchResults = await BlogService.getPosts({
  locale: 'zh-CN',
  search: '塔罗牌',
  limit: 5
})
```

### 创建博客文章

```typescript
import { BlogService } from '@/lib/blog-service'

const newPost = await BlogService.createPost({
  title: 'Understanding Tarot Cards',
  content: '<h1>Your content here</h1><p>Article content...</p>',
  excerpt: 'A brief description of the article',
  locale: 'en',
  category: 'tarot',
  tags: ['tarot', 'divination', 'spirituality'],
  status: 'PUBLISHED',
  seoTitle: 'SEO optimized title',
  seoDescription: 'SEO description',
  keywords: ['tarot', 'cards', 'reading']
})
```

### AI内容生成

```typescript
import { AIContentGenerator } from '@/lib/ai-content-generator'

const generator = new AIContentGenerator()

// 生成单篇文章
const article = await generator.generateArticle({
  topic: 'The Fool Card Meaning',
  category: 'tarot',
  locale: 'en',
  length: 'medium',
  includeTestRecommendation: true
})

// 批量生成文章
const batchResult = await generator.batchGenerateArticles([
  { topic: 'Tarot Basics', category: 'tarot', locale: 'en' },
  { topic: 'Astrology Guide', category: 'astrology', locale: 'en' }
])
```

## 组件使用指南

### 博客列表页面

```tsx
import BlogList from '@/components/blog/BlogList'
import BlogHero from '@/components/blog/BlogHero'
import BlogCategories from '@/components/blog/BlogCategories'

export default function BlogPage({ posts, searchParams }) {
  return (
    <div>
      <BlogHero locale="en" />
      <BlogCategories locale="en" currentCategory={searchParams.category} />
      <BlogList
        posts={posts}
        total={100}
        currentPage={1}
        totalPages={10}
        locale="en"
        searchParams={searchParams}
      />
    </div>
  )
}
```

### 博客文章详情页面

```tsx
import BlogArticle from '@/components/blog/BlogArticle'
import TestRecommendation from '@/components/blog/TestRecommendation'
import RelatedPosts from '@/components/blog/RelatedPosts'

export default function BlogPostPage({ post, relatedPosts }) {
  return (
    <div>
      <BlogArticle post={post} locale="en" categoryInfo={categoryInfo} />
      <TestRecommendation
        testType="TAROT"
        category="tarot"
        locale="en"
        context="article"
      />
      <RelatedPosts posts={relatedPosts} currentPostId={post.id} locale="en" />
    </div>
  )
}
```

## 多语言支持

### 支持的语言

- `en` - English (英语)
- `zh-CN` - 简体中文
- `zh-TW` - 繁体中文
- `es` - Español (西班牙语)
- `pt` - Português (葡萄牙语)
- `hi` - हिन्दी (印地语)
- `ja` - 日本語 (日语)

### 添加新语言

1. 在 `messages/` 目录下添加新的语言文件
2. 更新 `BLOG_CATEGORIES` 配置
3. 在 `TestIntegrationService` 中添加本地化支持

## SEO优化

### 自动SEO功能

- 自动生成SEO标题和描述
- 结构化数据支持
- 多语言hreflang标签
- 自动生成sitemap
- 优化的URL结构

### 手动SEO配置

```typescript
const post = await BlogService.createPost({
  // ... 其他字段
  seoTitle: '自定义SEO标题（60字符以内）',
  seoDescription: '自定义SEO描述（160字符以内）',
  keywords: ['关键词1', '关键词2', '关键词3']
})
```

## 测试导流集成

### 自动测试推荐

系统会根据文章内容自动推荐相关测试：

- 塔罗文章 → 塔罗测试
- 星座文章 → 星座测试
- 数字命理文章 → 数字命理测试

### 自定义测试推荐

```typescript
import { TestIntegrationService } from '@/lib/test-integration'

const recommendations = TestIntegrationService.getTestRecommendations(
  'tarot',
  articleContent,
  articleTitle,
  articleTags
)

const enhancedContent = TestIntegrationService.insertTestCTAs(
  originalContent,
  recommendations,
  'en'
)
```

## 性能优化

### 缓存策略

- 使用Next.js ISR进行静态生成
- Supabase查询优化
- 图片懒加载和优化

### 监控和分析

- 文章浏览量统计
- 用户行为分析
- 性能指标监控

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查Supabase配置
   - 确认网络连接

2. **AI生成失败**
   - 检查API密钥配置
   - 确认服务可用性

3. **多语言显示异常**
   - 检查语言文件
   - 确认locale参数

### 调试工具

```bash
# 运行系统测试
npm run test-blog-system

# 检查数据库连接
npm run check-db

# 验证AI服务
npm run test-ai-services
```

## 部署指南

### Vercel部署

1. 连接GitHub仓库
2. 配置环境变量
3. 自动部署

### 数据库迁移

```bash
# 运行Prisma迁移
npx prisma migrate deploy

# 生成类型定义
npx prisma generate
```

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 创建Pull Request

## 许可证

MIT License - 详见LICENSE文件

---

## 联系支持

如有问题或建议，请通过以下方式联系：

- 📧 Email: <EMAIL>
- 💬 GitHub Issues: [项目Issues页面]
- 📚 文档: [在线文档地址]
