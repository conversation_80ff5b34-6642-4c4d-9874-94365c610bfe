'use client'

import { motion } from 'framer-motion'
import { Sparkles, BookOpen, Star, Zap } from 'lucide-react'

interface BlogHeroProps {
  locale: string
}

export default function BlogHero({ locale }: BlogHeroProps) {
  const heroContent = {
    en: {
      title: 'Mystical Insights & Spiritual Guidance',
      subtitle: 'Discover ancient wisdom through modern AI-powered analysis. Explore tarot, astrology, numerology, and more.',
      stats: [
        { label: 'Articles Published', value: '500+', icon: BookOpen },
        { label: 'Readers Worldwide', value: '100K+', icon: Star },
        { label: 'Spiritual Topics', value: '6+', icon: Sparkles },
        { label: 'AI Insights', value: '∞', icon: Zap }
      ]
    },
    'zh-CN': {
      title: '神秘洞察与灵性指导',
      subtitle: '通过现代AI分析发现古老智慧。探索塔罗、占星、数字命理等更多内容。',
      stats: [
        { label: '已发布文章', value: '500+', icon: BookO<PERSON> },
        { label: '全球读者', value: '10万+', icon: Star },
        { label: '灵性主题', value: '6+', icon: Sparkles },
        { label: 'AI洞察', value: '∞', icon: Zap }
      ]
    }
  }
  
  const content = heroContent[locale as keyof typeof heroContent] || heroContent.en
  
  return (
    <section className="relative overflow-hidden bg-gradient-to-br from-mystical-50 via-white to-gold-50 dark:from-dark-900 dark:via-dark-800 dark:to-dark-700">
      {/* 背景装饰 */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-mystical-200/30 dark:bg-mystical-800/20 rounded-full blur-3xl" />
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gold-200/30 dark:bg-gold-800/20 rounded-full blur-3xl" />
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-purple-200/20 dark:bg-purple-800/10 rounded-full blur-3xl" />
      </div>
      
      {/* 星星装饰 */}
      <div className="absolute inset-0">
        {[...Array(20)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-mystical-400 dark:bg-mystical-300 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              opacity: [0.3, 1, 0.3],
              scale: [0.8, 1.2, 0.8],
            }}
            transition={{
              duration: 2 + Math.random() * 2,
              repeat: Infinity,
              delay: Math.random() * 2,
            }}
          />
        ))}
      </div>
      
      <div className="relative max-w-7xl mx-auto px-4 py-20 lg:py-32">
        <div className="text-center">
          {/* 主标题 */}
          <motion.h1
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-4xl md:text-5xl lg:text-6xl font-bold font-serif text-mystical-900 dark:text-white mb-6 leading-tight"
          >
            {content.title}
          </motion.h1>
          
          {/* 副标题 */}
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-lg md:text-xl text-mystical-600 dark:text-mystical-300 max-w-3xl mx-auto mb-12 leading-relaxed"
          >
            {content.subtitle}
          </motion.p>
          
          {/* 统计数据 */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto"
          >
            {content.stats.map((stat, index) => (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: 0.6 + index * 0.1 }}
                className="text-center"
              >
                <div className="inline-flex items-center justify-center w-16 h-16 bg-white dark:bg-dark-800 rounded-2xl shadow-lg border border-mystical-200 dark:border-dark-600 mb-4">
                  <stat.icon className="w-8 h-8 text-mystical-500" />
                </div>
                <div className="text-2xl md:text-3xl font-bold text-mystical-900 dark:text-white mb-1">
                  {stat.value}
                </div>
                <div className="text-sm text-mystical-600 dark:text-mystical-400">
                  {stat.label}
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </div>
      
      {/* 底部波浪 */}
      <div className="absolute bottom-0 left-0 w-full">
        <svg
          viewBox="0 0 1200 120"
          preserveAspectRatio="none"
          className="relative block w-full h-16 fill-white dark:fill-dark-900"
        >
          <path d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z"
            opacity=".25"
          />
          <path d="M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z"
            opacity=".5"
          />
          <path d="M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z" />
        </svg>
      </div>
    </section>
  )
}
