'use client'

import Link from 'next/link'
import Image from 'next/image'
import { motion } from 'framer-motion'
import { Clock, Eye, ArrowRight } from 'lucide-react'
import type { BlogPost } from '@prisma/client'
import { formatPublishDate, formatReadingTime, getCategoryInfo } from '@/lib/blog-utils'

interface RelatedPostsProps {
  posts: BlogPost[]
  currentPostId: string
  locale: string
}

export default function RelatedPosts({ posts, currentPostId, locale }: RelatedPostsProps) {
  // 过滤掉当前文章
  const relatedPosts = posts.filter(post => post.id !== currentPostId).slice(0, 3)
  
  if (relatedPosts.length === 0) {
    return null
  }
  
  return (
    <section className="max-w-4xl mx-auto px-4 py-16">
      <div className="text-center mb-12">
        <h2 className="text-3xl font-bold font-serif text-mystical-900 dark:text-white mb-4">
          Related Articles
        </h2>
        <p className="text-mystical-600 dark:text-mystical-300 max-w-2xl mx-auto">
          Continue your mystical journey with these carefully selected articles
        </p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {relatedPosts.map((post, index) => (
          <RelatedPostCard
            key={post.id}
            post={post}
            locale={locale}
            index={index}
          />
        ))}
      </div>
      
      {/* 查看更多按钮 */}
      <div className="text-center mt-12">
        <Link
          href={`/${locale}/blog`}
          className="inline-flex items-center space-x-2 px-6 py-3 bg-mystical-500 text-white rounded-full hover:bg-mystical-600 transition-colors font-medium"
        >
          <span>Explore More Articles</span>
          <ArrowRight className="w-4 h-4" />
        </Link>
      </div>
    </section>
  )
}

interface RelatedPostCardProps {
  post: BlogPost
  locale: string
  index: number
}

function RelatedPostCard({ post, locale, index }: RelatedPostCardProps) {
  const categoryInfo = getCategoryInfo(post.category)
  
  return (
    <motion.article
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      className="group"
    >
      <Link href={`/${locale}/blog/${post.slug}`}>
        <div className="bg-white dark:bg-dark-800 rounded-2xl overflow-hidden shadow-lg border border-mystical-200 dark:border-dark-600 hover:shadow-xl transition-all duration-300 group-hover:transform group-hover:-translate-y-1">
          {/* 封面图片 */}
          {post.coverImage && (
            <div className="relative aspect-video overflow-hidden">
              <Image
                src={post.coverImage}
                alt={post.title}
                fill
                className="object-cover group-hover:scale-105 transition-transform duration-300"
                sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 33vw"
              />
              
              {/* 分类标签 */}
              <div className="absolute top-4 left-4">
                <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-${categoryInfo.color}/90 text-white backdrop-blur-sm`}>
                  {categoryInfo.name}
                </span>
              </div>
            </div>
          )}
          
          {/* 内容区域 */}
          <div className="p-6">
            {/* 如果没有封面图片，显示分类标签 */}
            {!post.coverImage && (
              <div className="mb-3">
                <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-${categoryInfo.color}/10 text-${categoryInfo.color}`}>
                  {categoryInfo.name}
                </span>
              </div>
            )}
            
            {/* 标题 */}
            <h3 className="text-xl font-bold font-serif text-mystical-900 dark:text-white mb-3 line-clamp-2 group-hover:text-mystical-700 dark:group-hover:text-mystical-200 transition-colors">
              {post.title}
            </h3>
            
            {/* 摘要 */}
            {post.excerpt && (
              <p className="text-mystical-600 dark:text-mystical-300 mb-4 line-clamp-3 text-sm leading-relaxed">
                {post.excerpt}
              </p>
            )}
            
            {/* 元数据 */}
            <div className="flex items-center justify-between text-xs text-mystical-500 dark:text-mystical-400">
              <div className="flex items-center space-x-4">
                <span className="flex items-center">
                  <Clock className="w-3 h-3 mr-1" />
                  {formatReadingTime(post.readingTime, locale)}
                </span>
                <span className="flex items-center">
                  <Eye className="w-3 h-3 mr-1" />
                  {post.viewCount}
                </span>
              </div>
              
              <time dateTime={post.publishedAt?.toISOString()}>
                {formatPublishDate(post.publishedAt || post.createdAt, locale)}
              </time>
            </div>
            
            {/* 标签 */}
            {post.tags && post.tags.length > 0 && (
              <div className="flex flex-wrap gap-1 mt-3">
                {post.tags.slice(0, 3).map((tag) => (
                  <span
                    key={tag}
                    className="px-2 py-1 bg-mystical-100 dark:bg-dark-700 text-mystical-600 dark:text-mystical-300 rounded text-xs"
                  >
                    #{tag}
                  </span>
                ))}
                {post.tags.length > 3 && (
                  <span className="px-2 py-1 bg-mystical-100 dark:bg-dark-700 text-mystical-600 dark:text-mystical-300 rounded text-xs">
                    +{post.tags.length - 3}
                  </span>
                )}
              </div>
            )}
          </div>
        </div>
      </Link>
    </motion.article>
  )
}
