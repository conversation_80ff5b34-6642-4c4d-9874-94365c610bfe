import { NextRequest, NextResponse } from 'next/server'
import { BlogService } from '@/lib/blog-service'
import type { CreateBlogPostData } from '@/lib/blog-service'

/**
 * 根据slug获取博客文章详情
 * GET /api/blog/[slug]
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const { searchParams } = new URL(request.url)
    const locale = searchParams.get('locale') || 'en'
    
    const post = await BlogService.getPostBySlug(params.slug, locale)
    
    if (!post) {
      return NextResponse.json(
        {
          success: false,
          error: 'Post not found'
        },
        { status: 404 }
      )
    }
    
    return NextResponse.json({
      success: true,
      data: post
    })
    
  } catch (error) {
    console.error('获取博客文章详情失败:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch blog post',
        message: error.message
      },
      { status: 500 }
    )
  }
}

/**
 * 更新博客文章
 * PUT /api/blog/[slug]
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const body = await request.json()
    const { searchParams } = new URL(request.url)
    const locale = searchParams.get('locale') || 'en'
    
    // 首先获取现有文章以获得ID
    const existingPost = await BlogService.getPostBySlug(params.slug, locale)
    if (!existingPost) {
      return NextResponse.json(
        {
          success: false,
          error: 'Post not found'
        },
        { status: 404 }
      )
    }
    
    const updateData: Partial<CreateBlogPostData> = {
      title: body.title,
      slug: body.slug,
      content: body.content,
      excerpt: body.excerpt,
      coverImage: body.coverImage,
      locale: body.locale,
      category: body.category,
      tags: body.tags,
      status: body.status,
      publishedAt: body.publishedAt ? new Date(body.publishedAt) : undefined,
      seoTitle: body.seoTitle,
      seoDescription: body.seoDescription,
      keywords: body.keywords,
      metadata: body.metadata
    }
    
    // 移除undefined值
    Object.keys(updateData).forEach(key => {
      if (updateData[key as keyof typeof updateData] === undefined) {
        delete updateData[key as keyof typeof updateData]
      }
    })
    
    const updatedPost = await BlogService.updatePost(existingPost.id, updateData)
    
    return NextResponse.json({
      success: true,
      data: updatedPost
    })
    
  } catch (error) {
    console.error('更新博客文章失败:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update blog post',
        message: error.message
      },
      { status: 500 }
    )
  }
}

/**
 * 删除博客文章（软删除）
 * DELETE /api/blog/[slug]
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const { searchParams } = new URL(request.url)
    const locale = searchParams.get('locale') || 'en'
    
    // 首先获取现有文章以获得ID
    const existingPost = await BlogService.getPostBySlug(params.slug, locale)
    if (!existingPost) {
      return NextResponse.json(
        {
          success: false,
          error: 'Post not found'
        },
        { status: 404 }
      )
    }
    
    await BlogService.deletePost(existingPost.id)
    
    return NextResponse.json({
      success: true,
      message: 'Post deleted successfully'
    })
    
  } catch (error) {
    console.error('删除博客文章失败:', error)
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to delete blog post',
        message: error.message
      },
      { status: 500 }
    )
  }
}
