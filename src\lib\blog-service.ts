import { supabase } from './supabase'
import { TestIntegrationService } from './test-integration'
import type { BlogPost, PostStatus, TestType } from '@prisma/client'

// 博客文章接口定义
export interface CreateBlogPostData {
  title: string
  slug?: string
  content: string
  excerpt?: string
  coverImage?: string | undefined
  locale: string
  category: string
  tags: string[]
  status?: PostStatus
  publishedAt?: Date
  seoTitle?: string | undefined
  seoDescription?: string | undefined
  keywords?: string[] | undefined
  metadata?: any
}

export interface BlogPostWithMeta extends BlogPost {
  viewCount: number
  readingTime: number
}

export interface BlogListParams {
  locale?: string
  category?: string
  status?: PostStatus
  tags?: string[]
  search?: string
  page?: number
  limit?: number
  sortBy?: 'publishedAt' | 'viewCount' | 'createdAt'
  sortOrder?: 'asc' | 'desc'
}

export interface BlogListResponse {
  posts: BlogPostWithMeta[]
  total: number
  page: number
  limit: number
  totalPages: number
}

// AI生成内容接口
export interface AIGeneratedPost {
  title: string
  content: string
  excerpt: string
  locale: string
  category: string
  tags: string[]
  seoTitle?: string
  seoDescription?: string
  keywords: string[]
  coverImage?: string
  aiMetadata: {
    model: string
    generatedAt: Date
    prompt?: string
    confidence?: number
  }
}

/**
 * 博客服务类 - 基于Supabase API的数据库操作
 * 严格遵循数据库连接策略原则，使用Supabase API而非Prisma直连
 */
export class BlogService {
  /**
   * 创建博客文章
   */
  static async createPost(data: CreateBlogPostData): Promise<BlogPost> {
    try {
      // 1. 生成唯一slug
      const slug = data.slug || await this.generateUniqueSlug(data.title, data.locale)
      
      // 2. 生成摘要（如果未提供）
      const excerpt = data.excerpt || this.generateExcerpt(data.content)
      
      // 3. 计算阅读时间
      const readingTime = this.calculateReadingTime(data.content)
      
      // 4. SEO优化
      const seoData = await this.optimizeSEO(data)
      
      // 5. 集成测试导流内容
      const testRecommendations = TestIntegrationService.getTestRecommendations(
        data.category,
        data.content,
        data.title,
        data.tags
      )

      const enhancedContent = TestIntegrationService.insertTestCTAs(
        data.content,
        testRecommendations,
        data.locale
      )

      // 6. 创建文章
      const { data: post, error } = await supabase
        .from('blog_posts')
        .insert({
          title: data.title,
          slug,
          content: enhancedContent,
          excerpt,
          coverImage: data.coverImage,
          locale: data.locale,
          category: data.category,
          tags: data.tags,
          status: data.status || 'DRAFT',
          publishedAt: data.status === 'PUBLISHED' ? (data.publishedAt || new Date()) : null,
          readingTime,
          seoTitle: seoData.title,
          seoDescription: seoData.description,
          keywords: seoData.keywords,
          metadata: {
            ...data.metadata,
            testIntegration: {
              recommendations: testRecommendations,
              enhancedAt: new Date()
            }
          },
          viewCount: 0
        })
        .select()
        .single()
      
      if (error) throw error
      return post as BlogPost
      
    } catch (error) {
      console.error('创建博客文章失败:', error)
      throw new Error(`Failed to create blog post: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * AI生成内容直接入库
   */
  static async createPostFromAI(aiContent: AIGeneratedPost): Promise<BlogPost> {
    try {
      const postData: CreateBlogPostData = {
        title: aiContent.title,
        content: aiContent.content,
        excerpt: aiContent.excerpt,
        locale: aiContent.locale,
        category: aiContent.category,
        tags: aiContent.tags,
        seoTitle: aiContent.seoTitle,
        seoDescription: aiContent.seoDescription,
        keywords: aiContent.keywords,
        coverImage: aiContent.coverImage || undefined,
        status: 'DRAFT',
        metadata: {
          ai: aiContent.aiMetadata,
          source: 'ai_generated'
        }
      }
      
      return await this.createPost(postData)
      
    } catch (error) {
      console.error('AI内容入库失败:', error)
      throw new Error(`Failed to create post from AI content: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * 批量AI内容入库
   */
  static async batchCreateFromAI(aiContents: AIGeneratedPost[]): Promise<{
    successful: BlogPost[]
    failed: { content: AIGeneratedPost; error: string }[]
    total: number
  }> {
    const successful: BlogPost[] = []
    const failed: { content: AIGeneratedPost; error: string }[] = []
    
    for (const aiContent of aiContents) {
      try {
        const post = await this.createPostFromAI(aiContent)
        successful.push(post)
        
        // 批量处理间隔，避免API限制
        await new Promise(resolve => setTimeout(resolve, 100))
        
      } catch (error) {
        failed.push({
          content: aiContent,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }
    
    return {
      successful,
      failed,
      total: aiContents.length
    }
  }

  /**
   * 获取博客文章列表
   */
  static async getPosts(params: BlogListParams = {}): Promise<BlogListResponse> {
    try {
      const {
        locale = 'en',
        category,
        status = 'PUBLISHED',
        tags,
        search,
        page = 1,
        limit = 10,
        sortBy = 'publishedAt',
        sortOrder = 'desc'
      } = params
      
      let query = supabase
        .from('blog_posts')
        .select('*', { count: 'exact' })
        .eq('locale', locale)
        .eq('status', status)
      
      // 分类筛选
      if (category) {
        query = query.eq('category', category)
      }
      
      // 标签筛选
      if (tags && tags.length > 0) {
        query = query.overlaps('tags', tags)
      }
      
      // 搜索功能
      if (search) {
        query = query.or(`title.ilike.%${search}%,content.ilike.%${search}%,excerpt.ilike.%${search}%`)
      }
      
      // 排序
      query = query.order(sortBy, { ascending: sortOrder === 'asc' })
      
      // 分页
      const from = (page - 1) * limit
      const to = from + limit - 1
      query = query.range(from, to)
      
      const { data: posts, error, count } = await query
      
      if (error) throw error
      
      return {
        posts: posts as BlogPostWithMeta[],
        total: count || 0,
        page,
        limit,
        totalPages: Math.ceil((count || 0) / limit)
      }
      
    } catch (error) {
      console.error('获取博客列表失败:', error)
      throw new Error(`Failed to get blog posts: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * 根据slug获取文章详情
   */
  static async getPostBySlug(slug: string, locale: string = 'en'): Promise<BlogPost | null> {
    try {
      const { data: post, error } = await supabase
        .from('blog_posts')
        .select('*')
        .eq('slug', slug)
        .eq('locale', locale)
        .single()
      
      if (error) {
        if (error.code === 'PGRST116') return null // 未找到记录
        throw error
      }
      
      // 增加浏览量
      await this.incrementViewCount(post.id)
      
      return post as BlogPost
      
    } catch (error) {
      console.error('获取文章详情失败:', error)
      throw new Error(`Failed to get post by slug: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * 更新文章
   */
  static async updatePost(id: string, data: Partial<CreateBlogPostData>): Promise<BlogPost> {
    try {
      const updateData: any = { ...data }
      
      // 如果更新了内容，重新计算阅读时间
      if (data.content) {
        updateData.readingTime = this.calculateReadingTime(data.content)
      }
      
      // 如果更新了标题，可能需要更新slug
      if (data.title && !data.slug) {
        updateData.slug = await this.generateUniqueSlug(data.title, data.locale || 'en')
      }
      
      const { data: post, error } = await supabase
        .from('blog_posts')
        .update(updateData)
        .eq('id', id)
        .select()
        .single()
      
      if (error) throw error
      return post as BlogPost
      
    } catch (error) {
      console.error('更新文章失败:', error)
      throw new Error(`Failed to update post: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * 发布文章
   */
  static async publishPost(id: string): Promise<BlogPost> {
    try {
      const { data: post, error } = await supabase
        .from('blog_posts')
        .update({
          status: 'PUBLISHED',
          publishedAt: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single()
      
      if (error) throw error
      return post as BlogPost
      
    } catch (error) {
      console.error('发布文章失败:', error)
      throw new Error(`Failed to publish post: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * 删除文章（软删除）
   */
  static async deletePost(id: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('blog_posts')
        .update({ status: 'DELETED' })
        .eq('id', id)
      
      if (error) throw error
      
    } catch (error) {
      console.error('删除文章失败:', error)
      throw new Error(`Failed to delete post: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * 增加浏览量
   */
  private static async incrementViewCount(postId: string): Promise<void> {
    try {
      const { error } = await supabase.rpc('increment_view_count', {
        post_id: postId
      })
      
      if (error) {
        // 如果RPC函数不存在，使用常规更新
        const { data: post } = await supabase
          .from('blog_posts')
          .select('viewCount')
          .eq('id', postId)
          .single()
        
        if (post) {
          await supabase
            .from('blog_posts')
            .update({ viewCount: (post.viewCount || 0) + 1 })
            .eq('id', postId)
        }
      }
      
    } catch (error) {
      console.error('增加浏览量失败:', error)
      // 不抛出错误，避免影响主要功能
    }
  }

  /**
   * 生成唯一slug
   */
  private static async generateUniqueSlug(title: string, locale: string): Promise<string> {
    // 基础slug生成
    let baseSlug = title
      .toLowerCase()
      .replace(/[^\w\s-]/g, '') // 移除特殊字符
      .replace(/\s+/g, '-') // 空格替换为连字符
      .replace(/-+/g, '-') // 多个连字符合并为一个
      .trim()
    
    // 添加语言后缀
    baseSlug = `${baseSlug}-${locale}`
    
    // 检查唯一性
    let slug = baseSlug
    let counter = 1
    
    while (true) {
      const { data } = await supabase
        .from('blog_posts')
        .select('id')
        .eq('slug', slug)
        .limit(1)
      
      if (!data || data.length === 0) {
        break
      }
      
      slug = `${baseSlug}-${counter}`
      counter++
    }
    
    return slug
  }

  /**
   * 生成文章摘要
   */
  private static generateExcerpt(content: string, maxLength: number = 160): string {
    // 移除HTML标签
    const textContent = content.replace(/<[^>]*>/g, '')
    
    // 截取指定长度
    if (textContent.length <= maxLength) {
      return textContent
    }
    
    // 在单词边界截取
    const truncated = textContent.substring(0, maxLength)
    const lastSpaceIndex = truncated.lastIndexOf(' ')
    
    if (lastSpaceIndex > 0) {
      return truncated.substring(0, lastSpaceIndex) + '...'
    }
    
    return truncated + '...'
  }

  /**
   * 计算阅读时间（分钟）
   */
  private static calculateReadingTime(content: string): number {
    // 移除HTML标签
    const textContent = content.replace(/<[^>]*>/g, '')
    
    // 计算单词数（中英文混合）
    const wordCount = textContent.split(/\s+/).length
    const chineseCharCount = (textContent.match(/[\u4e00-\u9fff]/g) || []).length
    
    // 阅读速度：英文200词/分钟，中文300字/分钟
    const englishReadingTime = wordCount / 200
    const chineseReadingTime = chineseCharCount / 300
    
    const totalMinutes = Math.max(englishReadingTime, chineseReadingTime)
    
    return Math.max(1, Math.round(totalMinutes))
  }

  /**
   * SEO优化
   */
  private static async optimizeSEO(data: CreateBlogPostData): Promise<{
    title: string
    description: string
    keywords: string[]
  }> {
    return {
      title: data.seoTitle || data.title.substring(0, 60),
      description: data.seoDescription || data.excerpt?.substring(0, 160) || this.generateExcerpt(data.content),
      keywords: data.keywords || data.tags
    }
  }
}
