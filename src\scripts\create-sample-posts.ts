import { BlogService } from '@/lib/blog-service'
import type { CreateBlogPostData } from '@/lib/blog-service'

// 示例博文数据
const SAMPLE_POSTS: CreateBlogPostData[] = [
  // 英文版本
  {
    title: 'Understanding Tarot Card Meanings: A Beginner\'s Guide',
    content: `
      <h1>Understanding Tarot Card Meanings: A Beginner's Guide</h1>
      
      <p>Tarot cards have fascinated people for centuries, offering insights into our past, present, and future. Whether you're a complete beginner or looking to deepen your understanding, this comprehensive guide will help you navigate the mystical world of tarot.</p>
      
      <h2>What Are Tarot Cards?</h2>
      <p>Tarot is a deck of 78 cards divided into two main categories: the Major Arcana (22 cards) and the Minor Arcana (56 cards). Each card carries its own symbolism and meaning, creating a rich tapestry of wisdom and guidance.</p>
      
      <h3>The Major Arcana</h3>
      <p>The Major Arcana represents life's spiritual lessons and major life events. These cards include iconic images like The Fool, The Magician, Death, and The World. Each card tells a story of personal growth and transformation.</p>
      
      <h3>The Minor Arcana</h3>
      <p>The Minor Arcana deals with day-to-day experiences and is divided into four suits: Cups (emotions), Wands (creativity and passion), Swords (thoughts and communication), and Pentacles (material world and resources).</p>
      
      <h2>How to Read Tarot Cards</h2>
      <p>Reading tarot is both an art and a skill that develops over time. Here are some essential tips for beginners:</p>
      
      <ul>
        <li><strong>Start with simple spreads:</strong> Begin with one-card or three-card readings before attempting complex layouts.</li>
        <li><strong>Trust your intuition:</strong> While card meanings are important, your intuitive response is equally valuable.</li>
        <li><strong>Practice regularly:</strong> The more you work with the cards, the stronger your connection becomes.</li>
        <li><strong>Keep a journal:</strong> Record your readings to track patterns and improve your interpretation skills.</li>
      </ul>
      
      <h2>Common Tarot Spreads</h2>
      <p>Different spreads serve different purposes. The Celtic Cross is popular for comprehensive readings, while the Past-Present-Future spread offers insight into life's timeline. Choose spreads that resonate with your questions and experience level.</p>
      
      <h2>Building Your Tarot Practice</h2>
      <p>Developing a meaningful tarot practice takes time and dedication. Create a sacred space for your readings, cleanse your cards regularly, and approach each session with respect and openness. Remember, tarot is a tool for self-reflection and guidance, not fortune-telling.</p>
      
      <p>As you embark on your tarot journey, be patient with yourself and trust the process. The cards will reveal their wisdom when you're ready to receive it.</p>
    `,
    excerpt: 'Discover the mystical world of tarot cards with this comprehensive beginner\'s guide. Learn about Major and Minor Arcana, reading techniques, and how to build a meaningful tarot practice.',
    locale: 'en',
    category: 'tarot',
    tags: ['tarot', 'beginner-guide', 'card-meanings', 'divination', 'spirituality'],
    status: 'PUBLISHED',
    seoTitle: 'Tarot Card Meanings Guide - Learn to Read Tarot Cards',
    seoDescription: 'Complete beginner\'s guide to tarot card meanings. Learn about Major Arcana, Minor Arcana, reading techniques, and building your tarot practice.',
    keywords: ['tarot cards', 'tarot meanings', 'how to read tarot', 'tarot guide', 'divination'],
    coverImage: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=600&fit=crop'
  },
  
  // 简体中文版本
  {
    title: '塔罗牌含义解读：初学者完全指南',
    content: `
      <h1>塔罗牌含义解读：初学者完全指南</h1>
      
      <p>塔罗牌几个世纪以来一直吸引着人们，为我们的过去、现在和未来提供洞察。无论你是完全的初学者还是希望加深理解，这份全面的指南将帮助你探索塔罗的神秘世界。</p>
      
      <h2>什么是塔罗牌？</h2>
      <p>塔罗牌是一副由78张牌组成的卡牌，分为两个主要类别：大阿卡纳（22张牌）和小阿卡纳（56张牌）。每张牌都承载着自己的象征意义，创造出丰富的智慧和指导体系。</p>
      
      <h3>大阿卡纳</h3>
      <p>大阿卡纳代表人生的精神课程和重大生活事件。这些牌包括标志性的图像，如愚者、魔术师、死神和世界。每张牌都讲述着个人成长和转变的故事。</p>
      
      <h3>小阿卡纳</h3>
      <p>小阿卡纳处理日常经历，分为四个花色：圣杯（情感）、权杖（创造力和激情）、宝剑（思想和沟通）、星币（物质世界和资源）。</p>
      
      <h2>如何解读塔罗牌</h2>
      <p>解读塔罗既是一门艺术，也是一项随时间发展的技能。以下是初学者的一些基本建议：</p>
      
      <ul>
        <li><strong>从简单的牌阵开始：</strong>在尝试复杂布局之前，先从单张牌或三张牌解读开始。</li>
        <li><strong>相信你的直觉：</strong>虽然牌的含义很重要，但你的直觉反应同样宝贵。</li>
        <li><strong>定期练习：</strong>你与牌的接触越多，连接就越强。</li>
        <li><strong>记录日志：</strong>记录你的解读以追踪模式并提高解释技能。</li>
      </ul>
      
      <h2>常见塔罗牌阵</h2>
      <p>不同的牌阵服务于不同的目的。凯尔特十字牌阵适合全面解读，而过去-现在-未来牌阵提供对人生时间线的洞察。选择与你的问题和经验水平相符的牌阵。</p>
      
      <h2>建立你的塔罗实践</h2>
      <p>发展有意义的塔罗实践需要时间和奉献。为你的解读创造一个神圣的空间，定期净化你的牌，以尊重和开放的态度对待每次会话。记住，塔罗是自我反思和指导的工具，而不是算命。</p>
      
      <p>当你踏上塔罗之旅时，对自己要有耐心，相信这个过程。当你准备好接受时，牌会揭示它们的智慧。</p>
    `,
    excerpt: '通过这份全面的初学者指南探索塔罗牌的神秘世界。了解大阿卡纳和小阿卡纳、解读技巧，以及如何建立有意义的塔罗实践。',
    locale: 'zh-CN',
    category: 'tarot',
    tags: ['塔罗牌', '初学者指南', '牌义解读', '占卜', '灵性'],
    status: 'PUBLISHED',
    seoTitle: '塔罗牌含义指南 - 学习塔罗牌解读',
    seoDescription: '完整的塔罗牌含义初学者指南。学习大阿卡纳、小阿卡纳、解读技巧和建立塔罗实践。',
    keywords: ['塔罗牌', '塔罗含义', '如何解读塔罗', '塔罗指南', '占卜'],
    coverImage: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=600&fit=crop'
  },
  
  // 繁体中文版本
  {
    title: '塔羅牌含義解讀：初學者完全指南',
    content: `
      <h1>塔羅牌含義解讀：初學者完全指南</h1>
      
      <p>塔羅牌幾個世紀以來一直吸引著人們，為我們的過去、現在和未來提供洞察。無論你是完全的初學者還是希望加深理解，這份全面的指南將幫助你探索塔羅的神秘世界。</p>
      
      <h2>什麼是塔羅牌？</h2>
      <p>塔羅牌是一副由78張牌組成的卡牌，分為兩個主要類別：大阿卡納（22張牌）和小阿卡納（56張牌）。每張牌都承載著自己的象徵意義，創造出豐富的智慧和指導體系。</p>
      
      <h3>大阿卡納</h3>
      <p>大阿卡納代表人生的精神課程和重大生活事件。這些牌包括標誌性的圖像，如愚者、魔術師、死神和世界。每張牌都講述著個人成長和轉變的故事。</p>
      
      <h3>小阿卡納</h3>
      <p>小阿卡納處理日常經歷，分為四個花色：聖杯（情感）、權杖（創造力和激情）、寶劍（思想和溝通）、星幣（物質世界和資源）。</p>
      
      <h2>如何解讀塔羅牌</h2>
      <p>解讀塔羅既是一門藝術，也是一項隨時間發展的技能。以下是初學者的一些基本建議：</p>
      
      <ul>
        <li><strong>從簡單的牌陣開始：</strong>在嘗試複雜佈局之前，先從單張牌或三張牌解讀開始。</li>
        <li><strong>相信你的直覺：</strong>雖然牌的含義很重要，但你的直覺反應同樣寶貴。</li>
        <li><strong>定期練習：</strong>你與牌的接觸越多，連接就越強。</li>
        <li><strong>記錄日誌：</strong>記錄你的解讀以追蹤模式並提高解釋技能。</li>
      </ul>
      
      <h2>常見塔羅牌陣</h2>
      <p>不同的牌陣服務於不同的目的。凱爾特十字牌陣適合全面解讀，而過去-現在-未來牌陣提供對人生時間線的洞察。選擇與你的問題和經驗水平相符的牌陣。</p>
      
      <h2>建立你的塔羅實踐</h2>
      <p>發展有意義的塔羅實踐需要時間和奉獻。為你的解讀創造一個神聖的空間，定期淨化你的牌，以尊重和開放的態度對待每次會話。記住，塔羅是自我反思和指導的工具，而不是算命。</p>
      
      <p>當你踏上塔羅之旅時，對自己要有耐心，相信這個過程。當你準備好接受時，牌會揭示它們的智慧。</p>
    `,
    excerpt: '通過這份全面的初學者指南探索塔羅牌的神秘世界。了解大阿卡納和小阿卡納、解讀技巧，以及如何建立有意義的塔羅實踐。',
    locale: 'zh-TW',
    category: 'tarot',
    tags: ['塔羅牌', '初學者指南', '牌義解讀', '占卜', '靈性'],
    status: 'PUBLISHED',
    seoTitle: '塔羅牌含義指南 - 學習塔羅牌解讀',
    seoDescription: '完整的塔羅牌含義初學者指南。學習大阿卡納、小阿卡納、解讀技巧和建立塔羅實踐。',
    keywords: ['塔羅牌', '塔羅含義', '如何解讀塔羅', '塔羅指南', '占卜'],
    coverImage: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800&h=600&fit=crop'
  }
]

/**
 * 创建示例博文
 */
export async function createSamplePosts() {
  console.log('开始创建示例博文...')
  
  const results = {
    successful: [] as any[],
    failed: [] as any[]
  }
  
  for (const postData of SAMPLE_POSTS) {
    try {
      console.log(`创建博文: ${postData.title} (${postData.locale})`)
      
      const post = await BlogService.createPost(postData)
      results.successful.push({
        id: post.id,
        title: post.title,
        locale: post.locale,
        slug: post.slug
      })
      
      console.log(`✅ 成功创建: ${post.slug}`)
      
      // 添加延迟避免数据库压力
      await new Promise(resolve => setTimeout(resolve, 500))
      
    } catch (error) {
      console.error(`❌ 创建失败: ${postData.title}`, error)
      results.failed.push({
        title: postData.title,
        locale: postData.locale,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }
  
  console.log('\n📊 创建结果统计:')
  console.log(`✅ 成功: ${results.successful.length}`)
  console.log(`❌ 失败: ${results.failed.length}`)
  
  if (results.successful.length > 0) {
    console.log('\n🎉 成功创建的博文:')
    results.successful.forEach(post => {
      console.log(`- ${post.title} (${post.locale}) - /${post.locale}/blog/${post.slug}`)
    })
  }
  
  if (results.failed.length > 0) {
    console.log('\n💥 创建失败的博文:')
    results.failed.forEach(failure => {
      console.log(`- ${failure.title} (${failure.locale}): ${failure.error}`)
    })
  }
  
  return results
}

// 如果直接运行此脚本
if (require.main === module) {
  createSamplePosts()
    .then(() => {
      console.log('\n🎯 示例博文创建完成！')
      process.exit(0)
    })
    .catch((error) => {
      console.error('💥 创建示例博文时出错:', error)
      process.exit(1)
    })
}
