import { createClient } from '@supabase/supabase-js'

// Supabase 配置
// Supabase configuration

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

if (!supabaseUrl) {
  throw new Error('Missing NEXT_PUBLIC_SUPABASE_URL environment variable')
}

if (!supabaseAnonKey) {
  throw new Error('Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable')
}

// 客户端 Supabase 实例（用于前端）
// Client-side Supabase instance (for frontend)
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  },
  realtime: {
    params: {
      eventsPerSecond: 10
    }
  }
})

// 服务端 Supabase 实例（具有管理员权限）
// Server-side Supabase instance (with admin privileges)
export const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

// Supabase 连接测试函数
// Supabase connection test function
export async function testSupabaseConnection() {
  try {
    const { data, error } = await supabase
      .from('users')
      .select('count')
      .limit(1)
    
    if (error && error.code !== 'PGRST116') { // PGRST116 = table doesn't exist (expected before migration)
      throw error
    }
    
    console.log('✅ Supabase 连接成功 / Supabase connected successfully')
    return true
  } catch (error) {
    console.error('❌ Supabase 连接失败 / Supabase connection failed:', error)
    return false
  }
}

// 获取当前用户
// Get current user
export async function getCurrentUser() {
  try {
    const { data: { user }, error } = await supabase.auth.getUser()
    
    if (error) {
      throw error
    }
    
    return user
  } catch (error) {
    console.error('获取用户信息失败 / Failed to get user:', error)
    return null
  }
}

// 用户认证相关函数
// User authentication functions

export async function signUpWithEmail(email: string, password: string, metadata?: any) {
  try {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: metadata
      }
    })
    
    if (error) {
      throw error
    }
    
    return { user: data.user, session: data.session }
  } catch (error) {
    console.error('注册失败 / Sign up failed:', error)
    throw error
  }
}

export async function signInWithEmail(email: string, password: string) {
  try {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    })
    
    if (error) {
      throw error
    }
    
    return { user: data.user, session: data.session }
  } catch (error) {
    console.error('登录失败 / Sign in failed:', error)
    throw error
  }
}

export async function signOut() {
  try {
    const { error } = await supabase.auth.signOut()
    
    if (error) {
      throw error
    }
    
    console.log('✅ 用户已登出 / User signed out')
  } catch (error) {
    console.error('登出失败 / Sign out failed:', error)
    throw error
  }
}

// 文件存储相关函数
// File storage functions

export async function uploadFile(
  bucket: string,
  path: string,
  file: File,
  options?: {
    cacheControl?: string
    contentType?: string
    upsert?: boolean
  }
) {
  try {
    const { data, error } = await supabase.storage
      .from(bucket)
      .upload(path, file, {
        cacheControl: options?.cacheControl || '3600',
        contentType: options?.contentType || file.type,
        upsert: options?.upsert || false
      })
    
    if (error) {
      throw error
    }
    
    return data
  } catch (error) {
    console.error('文件上传失败 / File upload failed:', error)
    throw error
  }
}

export async function getFileUrl(bucket: string, path: string) {
  try {
    const { data } = supabase.storage
      .from(bucket)
      .getPublicUrl(path)
    
    return data.publicUrl
  } catch (error) {
    console.error('获取文件URL失败 / Failed to get file URL:', error)
    throw error
  }
}

export async function deleteFile(bucket: string, path: string) {
  try {
    const { error } = await supabase.storage
      .from(bucket)
      .remove([path])
    
    if (error) {
      throw error
    }
    
    console.log('✅ 文件删除成功 / File deleted successfully')
  } catch (error) {
    console.error('文件删除失败 / File deletion failed:', error)
    throw error
  }
}

// 实时订阅相关函数
// Real-time subscription functions

export function subscribeToTable(
  table: string,
  callback: (payload: any) => void,
  filter?: string
) {
  const channel = supabase
    .channel(`${table}-changes`)
    .on(
      'postgres_changes',
      {
        event: '*',
        schema: 'public',
        table: table,
        filter: filter
      },
      callback
    )
    .subscribe()
  
  return channel
}

// 导出类型
// Export types
export type { User, Session } from '@supabase/supabase-js'
