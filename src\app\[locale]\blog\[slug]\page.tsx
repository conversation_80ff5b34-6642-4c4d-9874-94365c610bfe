import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import { BlogService } from '@/lib/blog-service'
import { generateBlogSEO, formatPublishDate, formatReadingTime } from '@/lib/blog-utils'
import BlogArticle from '@/components/blog/BlogArticle'
import RelatedPosts from '@/components/blog/RelatedPosts'
import TestRecommendation from '@/components/blog/TestRecommendation'
import { getCategoryInfo } from '@/lib/blog-utils'

interface BlogPostPageProps {
  params: {
    locale: string
    slug: string
  }
}

// 生成动态元数据
export async function generateMetadata({ params }: BlogPostPageProps): Promise<Metadata> {
  try {
    const post = await BlogService.getPostBySlug(params.slug, params.locale)
    
    if (!post) {
      return {
        title: 'Post Not Found',
        description: 'The requested blog post could not be found.'
      }
    }
    
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://mystical-website.com'
    const seoConfig = generateBlogSEO(post, baseUrl)
    
    return {
      title: seoConfig.title,
      description: seoConfig.description,
      keywords: seoConfig.keywords,
      authors: [{ name: 'Mystical Website Team' }],
      openGraph: {
        title: seoConfig.title,
        description: seoConfig.description,
        type: 'article',
        publishedTime: post.publishedAt?.toISOString(),
        modifiedTime: post.updatedAt.toISOString(),
        authors: ['Mystical Website Team'],
        images: post.coverImage ? [
          {
            url: post.coverImage,
            width: 1200,
            height: 630,
            alt: post.title
          }
        ] : [],
        locale: params.locale,
        siteName: 'Mystical Website'
      },
      twitter: {
        card: 'summary_large_image',
        title: seoConfig.title,
        description: seoConfig.description,
        images: post.coverImage ? [post.coverImage] : [],
        creator: '@mystical_website'
      },
      alternates: {
        canonical: seoConfig.canonicalUrl
      },
      other: {
        'article:published_time': post.publishedAt?.toISOString() || '',
        'article:modified_time': post.updatedAt.toISOString(),
        'article:author': 'Mystical Website Team',
        'article:section': post.category,
        'article:tag': post.tags.join(', ')
      }
    }
  } catch (error) {
    console.error('生成元数据失败:', error)
    return {
      title: 'Blog Post',
      description: 'Read our latest mystical insights and guidance.'
    }
  }
}

export default async function BlogPostPage({ params }: BlogPostPageProps) {
  try {
    const post = await BlogService.getPostBySlug(params.slug, params.locale)
    
    if (!post || post.status !== 'PUBLISHED') {
      notFound()
    }
    
    // 获取相关文章
    const relatedPostsQuery = {
      locale: params.locale,
      category: post.category,
      tags: post.tags,
      excludeId: post.id,
      limit: 3
    }
    
    const relatedPostsResponse = await BlogService.getPosts({
      locale: params.locale,
      category: post.category,
      limit: 3
    })
    
    const categoryInfo = getCategoryInfo(post.category)
    
    // 结构化数据
    const structuredData = {
      '@context': 'https://schema.org',
      '@type': 'Article',
      headline: post.title,
      description: post.excerpt,
      image: post.coverImage,
      author: {
        '@type': 'Organization',
        name: 'Mystical Website Team'
      },
      publisher: {
        '@type': 'Organization',
        name: 'Mystical Website',
        logo: {
          '@type': 'ImageObject',
          url: `${process.env.NEXT_PUBLIC_APP_URL}/logo.png`
        }
      },
      datePublished: post.publishedAt?.toISOString(),
      dateModified: post.updatedAt.toISOString(),
      mainEntityOfPage: {
        '@type': 'WebPage',
        '@id': `${process.env.NEXT_PUBLIC_APP_URL}/${params.locale}/blog/${post.slug}`
      },
      articleSection: post.category,
      keywords: post.keywords?.join(', '),
      wordCount: post.content.replace(/<[^>]*>/g, '').split(/\s+/).length,
      timeRequired: `PT${post.readingTime}M`
    }
    
    return (
      <>
        {/* 结构化数据 */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(structuredData)
          }}
        />
        
        <div className="min-h-screen bg-white dark:bg-dark-900">
          {/* 主要文章内容 */}
          <BlogArticle 
            post={post}
            locale={params.locale}
            categoryInfo={categoryInfo}
          />
          
          {/* 测试推荐区域 */}
          {categoryInfo.relatedTest && (
            <TestRecommendation
              testType={categoryInfo.relatedTest}
              category={post.category}
              locale={params.locale}
              context="article"
            />
          )}
          
          {/* 相关文章 */}
          {relatedPostsResponse.posts.length > 0 && (
            <RelatedPosts
              posts={relatedPostsResponse.posts}
              currentPostId={post.id}
              locale={params.locale}
            />
          )}
        </div>
      </>
    )
  } catch (error) {
    console.error('加载博客文章失败:', error)
    notFound()
  }
}

// 生成静态路径（可选，用于预渲染热门文章）
export async function generateStaticParams() {
  try {
    // 获取热门文章进行预渲染
    const locales = ['en', 'zh-CN', 'zh-TW', 'es', 'pt', 'hi', 'ja']
    const paths: Array<{ locale: string; slug: string }> = []
    
    for (const locale of locales) {
      const response = await BlogService.getPosts({
        locale,
        status: 'PUBLISHED',
        limit: 10,
        sortBy: 'viewCount',
        sortOrder: 'desc'
      })
      
      for (const post of response.posts) {
        paths.push({
          locale,
          slug: post.slug
        })
      }
    }
    
    return paths
  } catch (error) {
    console.error('生成静态路径失败:', error)
    return []
  }
}
